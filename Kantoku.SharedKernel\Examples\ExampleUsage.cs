// using Kantoku.SharedKernel.BuildingBlocks;
// using Kantoku.SharedKernel.Events;
// using Kantoku.SharedKernel.Exceptions;
// using Kantoku.SharedKernel.Guards;
// using Kantoku.SharedKernel.Pagination;
// using Kantoku.SharedKernel.Results;
// using Kantoku.SharedKernel.Time;
// using Kantoku.SharedKernel.ValueObjects;

// namespace Kantoku.SharedKernel.Examples;

// /// <summary>
// /// Example usage of the SharedKernel components.
// /// This file demonstrates how to use the various building blocks and patterns.
// /// </summary>
// public static class ExampleUsage
// {
//     /// <summary>
//     /// Example of creating and using value objects
//     /// </summary>
//     public static void ValueObjectExamples()
//     {
//         // Email value object with validation
//         var email = Email.Create("<EMAIL>");
//         Console.WriteLine($"Email: {email}");

//         // Money value object with currency support
//         var price = Money.Create(100.50m, "USD");
//         var tax = Money.Create(10.05m, "USD");
//         var total = price + tax;
//         Console.WriteLine($"Total: {total}");

//         // Phone number with formatting
//         var phone = PhoneNumber.Create("+1234567890");
//         Console.WriteLine($"Phone: {phone.Format(PhoneNumberFormat.International)}");

//         // Address value object
//         var address = Address.Create("123 Main St", "Tokyo", "Japan", postalCode: "100-0001");
//         Console.WriteLine($"Address: {address.ToSingleLine()}");
//     }

//     /// <summary>
//     /// Example of using the Result pattern for error handling
//     /// </summary>
//     public static void ResultPatternExamples()
//     {
//         // Success result
//         var successResult = Result<string>.Success("Operation completed");
//         if (successResult.IsSuccess)
//         {
//             Console.WriteLine($"Success: {successResult.Value}");
//         }

//         // Failure result
//         var failureResult = Result<string>.Failure("Something went wrong");
//         if (failureResult.IsFailure)
//         {
//             Console.WriteLine($"Error: {failureResult.Error}");
//         }

//         // Chaining operations with Map and Bind
//         var result = Result<int>.Success(10)
//             .Map(x => x * 2)
//             .Bind(x => x > 15 ? Result<int>.Success(x) : Result<int>.Failure("Value too small"));

//         Console.WriteLine($"Chained result: {(result.IsSuccess ? result.Value.ToString() : result.Error)}");
//     }

//     /// <summary>
//     /// Example of using Guard clauses for validation
//     /// </summary>
//     public static void GuardClauseExamples()
//     {
//         try
//         {
//             var name = Guard.NotNullOrWhiteSpace("John Doe");
//             var age = Guard.Positive(25);
//             var score = Guard.InRange(85, 0, 100);

//             Console.WriteLine($"Validated: {name}, Age: {age}, Score: {score}");
//         }
//         catch (ArgumentException ex)
//         {
//             Console.WriteLine($"Validation failed: {ex.Message}");
//         }
//     }

//     /// <summary>
//     /// Example of using pagination
//     /// </summary>
//     public static void PaginationExamples()
//     {
//         // Create sample data
//         var users = Enumerable.Range(1, 100)
//             .Select(i => $"User{i}")
//             .ToList();

//         // Create paged request
//         var request = new PagedRequest(pageNumber: 2, pageSize: 10)
//             .OrderBy("Name");

//         // Create paged result
//         var pagedResult = PagedResult<string>.Create(users, request.PageNumber, request.PageSize);

//         Console.WriteLine($"Page {pagedResult.PageNumber} of {pagedResult.TotalPages}");
//         Console.WriteLine($"Items {pagedResult.FirstItemOnPage}-{pagedResult.LastItemOnPage} of {pagedResult.TotalCount}");
//         Console.WriteLine($"Items: {string.Join(", ", pagedResult.Items)}");
//     }

//     /// <summary>
//     /// Example of using time utilities
//     /// </summary>
//     public static void TimeUtilityExamples()
//     {
//         // System clock for production
//         IClock systemClock = new SystemClock();
//         Console.WriteLine($"Current time: {systemClock.UtcNow}");

//         // Fixed clock for testing
//         IClock fixedClock = new FixedClock(new DateTime(2023, 1, 1, 12, 0, 0, DateTimeKind.Utc));
//         Console.WriteLine($"Fixed time: {fixedClock.UtcNow}");

//         // Date extensions
//         var date = new DateTime(2023, 6, 15);
//         Console.WriteLine($"Start of month: {date.StartOfMonth()}");
//         Console.WriteLine($"End of month: {date.EndOfMonth()}");
//         Console.WriteLine($"Is weekend: {date.IsWeekend()}");
//         Console.WriteLine($"Relative time: {date.ToRelativeTimeString()}");
//     }

//     /// <summary>
//     /// Example of domain exceptions
//     /// </summary>
//     public static void DomainExceptionExamples()
//     {
//         try
//         {
//             // Business rule violation
//             throw new BusinessRuleViolationException("UserMustBeActive", "User must be active to perform this operation")
//                 .WithDetail("UserId", Guid.NewGuid())
//                 .WithDetail("UserStatus", "Inactive");
//         }
//         catch (DomainException ex)
//         {
//             Console.WriteLine($"Domain exception: {ex.GetFormattedMessage()}");
//         }

//         try
//         {
//             // Entity not found
//             throw new EntityNotFoundException(typeof(User), Guid.NewGuid());
//         }
//         catch (EntityNotFoundException ex)
//         {
//             Console.WriteLine($"Entity not found: {ex.Message}");
//         }
//     }
// }

// /// <summary>
// /// Example entity using SharedKernel building blocks
// /// </summary>
// public class User : Entity, IAggregateRoot
// {
//     public Email Email { get; private set; }
//     public string Name { get; private set; }
//     public DateTime CreatedAt { get; private set; }

//     private User(Guid id, Email email, string name, IClock clock) : base(id)
//     {
//         Email = Guard.NotNull(email);
//         Name = Guard.NotNullOrWhiteSpace(name);
//         CreatedAt = clock.UtcNow;

//         // Raise domain event
//         AddDomainEvent(new UserCreatedEvent(Id, Email, Name));
//     }

//     public static Result<User> Create(Email email, string name, IClock clock)
//     {
//         try
//         {
//             var user = new User(Guid.NewGuid(), email, name, clock);
//             return Result<User>.Success(user);
//         }
//         catch (Exception ex)
//         {
//             return Result<User>.Failure($"Failed to create user: {ex.Message}");
//         }
//     }

//     public Result UpdateEmail(Email newEmail)
//     {
//         try
//         {
//             var oldEmail = Email;
//             Email = Guard.NotNull(newEmail);

//             AddDomainEvent(new UserEmailUpdatedEvent(Id, oldEmail, newEmail));
//             return Result.Success();
//         }
//         catch (Exception ex)
//         {
//             return Result.Failure($"Failed to update email: {ex.Message}");
//         }
//     }
// }

// /// <summary>
// /// Example domain event
// /// </summary>
// public class UserCreatedEvent : DomainEvent
// {
//     public Guid UserId { get; }
//     public Email Email { get; }
//     public string Name { get; }

//     public UserCreatedEvent(Guid userId, Email email, string name)
//     {
//         UserId = userId;
//         Email = email;
//         Name = name;
//     }
// }

// /// <summary>
// /// Example domain event
// /// </summary>
// public class UserEmailUpdatedEvent : DomainEvent
// {
//     public Guid UserId { get; }
//     public Email OldEmail { get; }
//     public Email NewEmail { get; }

//     public UserEmailUpdatedEvent(Guid userId, Email oldEmail, Email newEmail)
//     {
//         UserId = userId;
//         OldEmail = oldEmail;
//         NewEmail = newEmail;
//     }
// }

// /// <summary>
// /// Example domain event handler
// /// </summary>
// public class UserCreatedEventHandler : DomainEventHandler<UserCreatedEvent>
// {
//     public override async Task Handle(UserCreatedEvent domainEvent, CancellationToken cancellationToken)
//     {
//         LogEventHandling(domainEvent, "Sending welcome email");

//         // Simulate sending welcome email
//         await Task.Delay(100, cancellationToken);

//         Console.WriteLine($"Welcome email sent to {domainEvent.Email} for user {domainEvent.Name}");
//     }
// }

// /// <summary>
// /// Example enumeration using the Enumeration base class
// /// </summary>
// public class UserStatus : Enumeration
// {
//     public static readonly UserStatus Active = new(1, nameof(Active));
//     public static readonly UserStatus Inactive = new(2, nameof(Inactive));
//     public static readonly UserStatus Suspended = new(3, nameof(Suspended));
//     public static readonly UserStatus Deleted = new(4, nameof(Deleted));

//     private UserStatus(int id, string name) : base(id, name)
//     {
//     }

//     public bool CanLogin() => this == Active;
//     public bool CanBeReactivated() => this == Inactive || this == Suspended;
// }
