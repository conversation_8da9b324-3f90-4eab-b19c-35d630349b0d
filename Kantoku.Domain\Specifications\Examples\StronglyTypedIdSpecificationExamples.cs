using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.OrganizationManagement;
using Kantoku.Domain.ProjectManagement;
using Kantoku.Domain.CustomerManagement;
using Kantoku.Domain.ContractorManagement;

namespace Kantoku.Domain.Specifications.Examples;

/// <summary>
/// Example specifications demonstrating how to use strongly-typed IDs with the specification pattern
/// </summary>
public static class StronglyTypedIdSpecificationExamples
{
    /// <summary>
    /// Example: Find employees in specific organizations using strongly-typed IDs
    /// </summary>
    public static ISpecification<Employee> EmployeesInOrganizations(params OrganizationId[] organizationIds)
    {
        return StronglyTypedIdSpecificationExtensions.WithIdIn<Employee, OrganizationId>(
            organizationIds,
            employee => employee.OrgId);
    }

    /// <summary>
    /// Example: Find projects for specific customers using strongly-typed IDs
    /// </summary>
    public static ISpecification<Project> ProjectsForCustomers(params CustomerId[] customerIds)
    {
        return StronglyTypedIdSpecificationExtensions.WithNullableIdIn<Project, CustomerId>(
            customerIds,
            project => project.CustomerId);
    }

    /// <summary>
    /// Example: Find projects assigned to specific contractors using strongly-typed IDs
    /// </summary>
    public static ISpecification<Project> ProjectsForContractors(params ContractorId[] contractorIds)
    {
        return StronglyTypedIdSpecificationExtensions.WithNullableIdIn<Project, ContractorId>(
            contractorIds,
            project => project.ContractorId);
    }

    /// <summary>
    /// Example: Complex specification using builder pattern with strongly-typed IDs
    /// </summary>
    public static ISpecification<Employee> ActiveEmployeesInOrganizationWithSpecificStructures(
        OrganizationId organizationId,
        params StructureId[] structureIds)
    {
        return new StronglyTypedIdSpecificationBuilder<Employee>()
            .WithId(organizationId, emp => emp.OrgId)
            .WithNullableIdIn(structureIds, emp => emp.StructureId)
            .And(new ActiveEmployeeSpecification())
            .Build();
    }

    /// <summary>
    /// Example: Find employees with specific positions and rankings
    /// </summary>
    public static ISpecification<Employee> EmployeesWithPositionAndRanking(
        PositionId positionId,
        RankingId rankingId)
    {
        return new StronglyTypedIdSpecificationBuilder<Employee>()
            .WithNullableId(positionId, emp => emp.PositionId)
            .WithNullableId(rankingId, emp => emp.RankingId)
            .Build();
    }

    /// <summary>
    /// Example: Find projects in organization with specific type and customer
    /// </summary>
    public static ISpecification<Project> ProjectsInOrganizationWithTypeAndCustomer(
        OrganizationId organizationId,
        ProjectTypeId projectTypeId,
        CustomerId customerId)
    {
        return new StronglyTypedIdSpecificationBuilder<Project>()
            .WithId(organizationId, proj => proj.OrgId)
            .WithNullableId(projectTypeId, proj => proj.ProjectTypeId)
            .WithNullableId(customerId, proj => proj.CustomerId)
            .Build();
    }

    /// <summary>
    /// Example: Combining strongly-typed ID specifications with business logic specifications
    /// </summary>
    public static ISpecification<Project> CriticalProjectsForCustomer(CustomerId customerId)
    {
        var customerSpec = new ProjectForCustomerSpecification(customerId);
        var criticalSpec = new CriticalProjectSpecification();
        
        return customerSpec.And(criticalSpec);
    }

    /// <summary>
    /// Example: Using OR logic with strongly-typed IDs
    /// </summary>
    public static ISpecification<Employee> EmployeesInMultipleOrganizations(
        OrganizationId org1,
        OrganizationId org2)
    {
        var spec1 = new EmployeeInOrganizationSpecification(org1);
        var spec2 = new EmployeeInOrganizationSpecification(org2);
        
        return spec1.Or(spec2);
    }

    /// <summary>
    /// Example: Complex business rule with multiple strongly-typed IDs
    /// </summary>
    public static ISpecification<Employee> EligibleProjectManagersInOrganization(
        OrganizationId organizationId,
        params StructureId[] managementStructureIds)
    {
        return new StronglyTypedIdSpecificationBuilder<Employee>()
            .WithId(organizationId, emp => emp.OrgId)
            .WithNullableIdIn(managementStructureIds, emp => emp.StructureId)
            .And(new ActiveEmployeeSpecification())
            .And(new EmployeeWithApprovalAuthoritySpecification())
            .And(new OrganizationAdminEmployeeSpecification().Not()) // Not org admin (to avoid conflicts)
            .Build();
    }

    /// <summary>
    /// Example: Specification for projects that need attention from specific contractors
    /// </summary>
    public static ISpecification<Project> ProjectsNeedingAttentionFromContractors(
        params ContractorId[] contractorIds)
    {
        var contractorSpec = StronglyTypedIdSpecificationExtensions.WithNullableIdIn<Project, ContractorId>(
            contractorIds,
            project => project.ContractorId);
        
        var attentionSpec = new ProjectRequiringAttentionSpecification();
        
        return contractorSpec.And(attentionSpec);
    }
}

/// <summary>
/// Example usage and best practices for strongly-typed ID specifications
/// </summary>
public static class UsageExamples
{
    /// <summary>
    /// Example of how to use the specifications in a service or repository
    /// </summary>
    public static void ExampleUsage()
    {
        // Create strongly-typed IDs
        var orgId = OrganizationId.From(Guid.NewGuid());
        var customerId = CustomerId.From(Guid.NewGuid());
        var contractorId = ContractorId.From(Guid.NewGuid());

        // Use simple specifications
        var employeesInOrg = new EmployeeInOrganizationSpecification(orgId);
        var projectsForCustomer = new ProjectForCustomerSpecification(customerId);

        // Use complex specifications with builder
        var complexSpec = new StronglyTypedIdSpecificationBuilder<Project>()
            .WithId(orgId, p => p.OrgId)
            .WithNullableId(customerId, p => p.CustomerId)
            .And(new ActiveProjectSpecification())
            .Build();

        // Use extension methods for collections
        var multipleCustomers = new[] { customerId, CustomerId.From(Guid.NewGuid()) };
        var projectsForMultipleCustomers = StronglyTypedIdSpecificationExtensions
            .WithNullableIdIn<Project, CustomerId>(
                multipleCustomers,
                p => p.CustomerId);

        // Combine with business logic
        var criticalProjectsForCustomer = StronglyTypedIdSpecificationExamples
            .CriticalProjectsForCustomer(customerId);
    }
}
