using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.ContractorManagement.Enums;

/// <summary>
/// Enumeration representing contractor status
/// </summary>
public class ContractorStatus : Enumeration
{
    public static readonly ContractorStatus Active = new(1, nameof(Active), "Active", true);
    public static readonly ContractorStatus Inactive = new(2, nameof(Inactive), "Inactive", false);
    public static readonly ContractorStatus Qualified = new(3, nameof(Qualified), "Qualified", true);
    public static readonly ContractorStatus Unqualified = new(4, nameof(Unqualified), "Unqualified", false);
    public static readonly ContractorStatus Blacklisted = new(5, nameof(Blacklisted), "Blacklisted", false);
    public static readonly ContractorStatus Suspended = new(6, nameof(Suspended), "Suspended", false);

    public string DisplayName { get; private set; }
    public bool CanWork { get; private set; }

    private ContractorStatus(int id, string name, string displayName, bool canWork) : base(id, name)
    {
        DisplayName = displayName;
        CanWork = canWork;
    }

    public static IEnumerable<ContractorStatus> GetAll()
    {
        return new[] { Active, Inactive, Qualified, Unqualified, Blacklisted, Suspended };
    }

    public static ContractorStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown contractor status: {name}");
        return status;
    }

    public static ContractorStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown contractor status ID: {id}");
        return status;
    }

    public static ContractorStatus FromDisplayName(string displayName)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.DisplayName, displayName, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown contractor status display name: {displayName}");
        return status;
    }

    /// <summary>
    /// Checks if the status allows the contractor to be assigned to projects
    /// </summary>
    public bool CanBeAssignedToProjects => CanWork && this != Suspended;

    /// <summary>
    /// Checks if the status allows the contractor to submit bids
    /// </summary>
    public bool CanSubmitBids => CanWork && this != Suspended && this != Unqualified;

    /// <summary>
    /// Checks if the status requires approval for project assignment
    /// </summary>
    public bool RequiresApprovalForAssignment => this == Qualified || this == Active;
}
