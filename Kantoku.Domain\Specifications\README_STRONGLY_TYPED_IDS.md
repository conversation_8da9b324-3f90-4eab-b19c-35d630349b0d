# Specifications with Strongly-Typed IDs

This document explains how the specification pattern has been implemented to work with strongly-typed IDs in the Kantoku domain layer.

## Overview

The specification pattern implementation has been updated to support strongly-typed IDs while maintaining backward compatibility with primitive `Guid` types. This ensures type safety and prevents mixing different types of IDs while providing a smooth migration path.

## Key Features

### 1. Dual Specification Support

Each specification that works with IDs now has two versions:

- **Strongly-typed version**: Uses strongly-typed IDs (e.g., `OrganizationId`, `EmployeeId`)
- **Backward-compatible version**: Uses primitive `Guid` types (suffixed with `ByGuid`)

```csharp
// Strongly-typed version (preferred)
var spec1 = new EmployeeInOrganizationSpecification(organizationId);

// Backward-compatible version
var spec2 = new EmployeeInOrganizationByGuidSpecification(orgGuid);
```

### 2. Extension Methods for Complex Scenarios

The `StronglyTypedIdSpecificationExtensions` class provides helper methods for common patterns:

```csharp
// Match by single ID
var spec = StronglyTypedIdSpecificationExtensions.WithId<Employee, OrganizationId>(
    orgId, 
    emp => emp.OrgId);

// Match by collection of IDs
var spec = StronglyTypedIdSpecificationExtensions.WithIdIn<Employee, StructureId>(
    structureIds, 
    emp => emp.StructureId);

// Match nullable IDs
var spec = StronglyTypedIdSpecificationExtensions.WithNullableId<Project, CustomerId>(
    customerId, 
    proj => proj.CustomerId);
```

### 3. Builder Pattern for Complex Specifications

The `StronglyTypedIdSpecificationBuilder<T>` provides a fluent interface for building complex specifications:

```csharp
var spec = new StronglyTypedIdSpecificationBuilder<Employee>()
    .WithId(organizationId, emp => emp.OrgId)
    .WithNullableId(structureId, emp => emp.StructureId)
    .And(new ActiveEmployeeSpecification())
    .Build();
```

## Updated Specifications

### Employee Specifications

| Specification | Strongly-Typed Version | Backward-Compatible Version |
|---------------|------------------------|----------------------------|
| Organization | `EmployeeInOrganizationSpecification(OrganizationId)` | `EmployeeInOrganizationByGuidSpecification(Guid)` |
| Structure | `EmployeeInStructureSpecification(StructureId)` | `EmployeeInStructureByGuidSpecification(Guid)` |
| Position | `EmployeeWithPositionSpecification(PositionId)` | `EmployeeWithPositionByGuidSpecification(Guid)` |
| Ranking | `EmployeeWithRankingSpecification(RankingId)` | `EmployeeWithRankingByGuidSpecification(Guid)` |

### Project Specifications

| Specification | Strongly-Typed Version | Backward-Compatible Version |
|---------------|------------------------|----------------------------|
| Organization | `ProjectInOrganizationSpecification(OrganizationId)` | `ProjectInOrganizationByGuidSpecification(Guid)` |
| Customer | `ProjectForCustomerSpecification(CustomerId)` | `ProjectForCustomerByGuidSpecification(Guid)` |
| Contractor | `ProjectForContractorSpecification(ContractorId)` | `ProjectForContractorByGuidSpecification(Guid)` |
| Project Type | `ProjectWithTypeSpecification(ProjectTypeId)` | `ProjectWithTypeByGuidSpecification(Guid)` |

## Best Practices

### 1. Use Strongly-Typed Versions

Always prefer the strongly-typed versions for new code:

```csharp
// ✅ Good - Type-safe
var orgId = OrganizationId.From(guid);
var spec = new EmployeeInOrganizationSpecification(orgId);

// ❌ Avoid - Not type-safe
var spec = new EmployeeInOrganizationByGuidSpecification(guid);
```

### 2. Leverage Extension Methods for Collections

Use extension methods when working with collections of IDs:

```csharp
// ✅ Good - Clean and type-safe
var customerIds = new[] { customerId1, customerId2 };
var spec = StronglyTypedIdSpecificationExtensions.WithNullableIdIn<Project, CustomerId>(
    customerIds, 
    p => p.CustomerId);

// ❌ Avoid - Manual OR chaining
var spec = new ProjectForCustomerSpecification(customerId1)
    .Or(new ProjectForCustomerSpecification(customerId2));
```

### 3. Use Builder Pattern for Complex Logic

For specifications with multiple conditions, use the builder pattern:

```csharp
// ✅ Good - Readable and maintainable
var spec = new StronglyTypedIdSpecificationBuilder<Employee>()
    .WithId(organizationId, emp => emp.OrgId)
    .WithNullableIdIn(structureIds, emp => emp.StructureId)
    .And(new ActiveEmployeeSpecification())
    .And(new EmployeeWithApprovalAuthoritySpecification())
    .Build();
```

### 4. Combine with Business Logic Specifications

Mix ID-based specifications with business logic specifications:

```csharp
var customerSpec = new ProjectForCustomerSpecification(customerId);
var criticalSpec = new CriticalProjectSpecification();
var result = customerSpec.And(criticalSpec);
```

## Migration Guide

### For Existing Code

1. **Immediate**: Continue using existing specifications with `Guid` parameters
2. **Gradual**: Replace with strongly-typed versions as you refactor
3. **New Code**: Always use strongly-typed versions

### Example Migration

```csharp
// Before
var spec = new EmployeeInOrganizationSpecification(orgGuid);

// After
var orgId = OrganizationId.From(orgGuid);
var spec = new EmployeeInOrganizationSpecification(orgId);
```

## Expression Tree Considerations

The implementation correctly handles expression trees for Entity Framework compatibility:

- Strongly-typed IDs are converted to their underlying `Guid` values in expressions
- The `Value` property is used to extract the `Guid` for database queries
- Implicit conversions are leveraged where possible

## Performance Notes

- No performance overhead: Strongly-typed IDs compile to the same IL as `Guid`
- Expression trees are optimized by the compiler
- Database queries remain unchanged (still use `Guid` comparisons)

## Examples

See `Kantoku.Domain/Specifications/Examples/StronglyTypedIdSpecificationExamples.cs` for comprehensive usage examples.
