using System.Linq.Expressions;

namespace Kantoku.SharedKernel.Specifications;

/// <summary>
/// Interface for specifications pattern
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public interface ISpecification<T>
{
    /// <summary>
    /// Gets the expression that represents the specification
    /// </summary>
    Expression<Func<T, bool>> ToExpression();

    /// <summary>
    /// Checks if the specification is satisfied by the given entity
    /// </summary>
    bool IsSatisfiedBy(T entity);

    /// <summary>
    /// Combines this specification with another using AND logic
    /// </summary>
    ISpecification<T> And(ISpecification<T> specification);

    /// <summary>
    /// Combines this specification with another using OR logic
    /// </summary>
    ISpecification<T> Or(ISpecification<T> specification);

    /// <summary>
    /// Negates this specification
    /// </summary>
    ISpecification<T> Not();
}

/// <summary>
/// Base class for specifications
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public abstract class Specification<T> : ISpecification<T>
{
    /// <summary>
    /// Gets the expression that represents the specification
    /// </summary>
    public abstract Expression<Func<T, bool>> ToExpression();

    /// <summary>
    /// Checks if the specification is satisfied by the given entity
    /// </summary>
    public bool IsSatisfiedBy(T entity)
    {
        var predicate = ToExpression().Compile();
        return predicate(entity);
    }

    /// <summary>
    /// Combines this specification with another using AND logic
    /// </summary>
    public ISpecification<T> And(ISpecification<T> specification)
    {
        return new AndSpecification<T>(this, specification);
    }

    /// <summary>
    /// Combines this specification with another using OR logic
    /// </summary>
    public ISpecification<T> Or(ISpecification<T> specification)
    {
        return new OrSpecification<T>(this, specification);
    }

    /// <summary>
    /// Negates this specification
    /// </summary>
    public ISpecification<T> Not()
    {
        return new NotSpecification<T>(this);
    }

    /// <summary>
    /// Implicit conversion to expression
    /// </summary>
    public static implicit operator Expression<Func<T, bool>>(Specification<T> specification)
    {
        return specification.ToExpression();
    }
}

/// <summary>
/// Specification that combines two specifications with AND logic
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class AndSpecification<T> : Specification<T>
{
    private readonly ISpecification<T> _left;
    private readonly ISpecification<T> _right;

    /// <summary>
    /// Initializes a new instance of the AndSpecification class
    /// </summary>
    /// <param name="left">The left specification</param>
    /// <param name="right">The right specification</param>
    public AndSpecification(ISpecification<T> left, ISpecification<T> right)
    {
        _left = left ?? throw new ArgumentNullException(nameof(left));
        _right = right ?? throw new ArgumentNullException(nameof(right));
    }

    /// <summary>
    /// Gets the expression that represents the AND combination
    /// </summary>
    /// <returns>The combined expression</returns>
    public override Expression<Func<T, bool>> ToExpression()
    {
        var leftExpression = _left.ToExpression();
        var rightExpression = _right.ToExpression();

        var parameter = Expression.Parameter(typeof(T));
        var leftVisitor = new ReplaceExpressionVisitor(leftExpression.Parameters[0], parameter);
        var left = leftVisitor.Visit(leftExpression.Body);

        var rightVisitor = new ReplaceExpressionVisitor(rightExpression.Parameters[0], parameter);
        var right = rightVisitor.Visit(rightExpression.Body);

        return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(left!, right!), parameter);
    }
}

/// <summary>
/// Specification that combines two specifications with OR logic
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class OrSpecification<T> : Specification<T>
{
    private readonly ISpecification<T> _left;
    private readonly ISpecification<T> _right;

    /// <summary>
    /// Initializes a new instance of the OrSpecification class
    /// </summary>
    /// <param name="left">The left specification</param>
    /// <param name="right">The right specification</param>
    public OrSpecification(ISpecification<T> left, ISpecification<T> right)
    {
        _left = left ?? throw new ArgumentNullException(nameof(left));
        _right = right ?? throw new ArgumentNullException(nameof(right));
    }

    /// <summary>
    /// Gets the expression that represents the OR combination
    /// </summary>
    /// <returns>The combined expression</returns>
    public override Expression<Func<T, bool>> ToExpression()
    {
        var leftExpression = _left.ToExpression();
        var rightExpression = _right.ToExpression();

        var parameter = Expression.Parameter(typeof(T));
        var leftVisitor = new ReplaceExpressionVisitor(leftExpression.Parameters[0], parameter);
        var left = leftVisitor.Visit(leftExpression.Body);

        var rightVisitor = new ReplaceExpressionVisitor(rightExpression.Parameters[0], parameter);
        var right = rightVisitor.Visit(rightExpression.Body);

        return Expression.Lambda<Func<T, bool>>(Expression.OrElse(left!, right!), parameter);
    }
}

/// <summary>
/// Specification that negates another specification
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class NotSpecification<T> : Specification<T>
{
    private readonly ISpecification<T> _specification;

    /// <summary>
    /// Initializes a new instance of the NotSpecification class
    /// </summary>
    /// <param name="specification">The specification to negate</param> 
    public NotSpecification(ISpecification<T> specification)
    {
        _specification = specification ?? throw new ArgumentNullException(nameof(specification));
    }

    /// <summary>
    /// Gets the expression that represents the negation
    /// </summary>
    /// <returns>The negated expression</returns>   
    public override Expression<Func<T, bool>> ToExpression()
    {
        var expression = _specification.ToExpression();
        var parameter = expression.Parameters[0];
        var body = Expression.Not(expression.Body);

        return Expression.Lambda<Func<T, bool>>(body, parameter);
    }
}

/// <summary>
/// Expression visitor for replacing parameters
/// </summary>
internal class ReplaceExpressionVisitor : ExpressionVisitor
{
    private readonly Expression _oldValue;
    private readonly Expression _newValue;

    public ReplaceExpressionVisitor(Expression oldValue, Expression newValue)
    {
        _oldValue = oldValue;
        _newValue = newValue;
    }

    public override Expression? Visit(Expression? node)
    {
        if (node == _oldValue)
            return _newValue;

        return base.Visit(node);
    }
}

/// <summary>
/// Specification that always returns true
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class TrueSpecification<T> : Specification<T>
{
    /// <inheritdoc/>
    public override Expression<Func<T, bool>> ToExpression()
    {
        return x => true;
    }
}

/// <summary>
/// Specification that always returns false
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class FalseSpecification<T> : Specification<T>
{
    /// <summary>
    /// Gets the expression that represents the specification
    /// </summary>
    /// <returns></returns>
    public override Expression<Func<T, bool>> ToExpression()
    {
        return x => false;
    }
}

/// <summary>
/// Specification based on a predicate expression
/// </summary>
/// <typeparam name="T">The type to apply the specification to</typeparam>
public class ExpressionSpecification<T> : Specification<T>
{
    private readonly Expression<Func<T, bool>> _expression;
    
    /// <summary>
    ///  Initializes a new instance of the ExpressionSpecification class
    /// </summary>
    /// <param name="expression"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public ExpressionSpecification(Expression<Func<T, bool>> expression)
    {
        _expression = expression ?? throw new ArgumentNullException(nameof(expression));
    }

    /// <summary>
    /// Gets the expression that represents the specification
    /// </summary>
    /// <returns></returns>
    public override Expression<Func<T, bool>> ToExpression()
    {
        return _expression;
    }
}
