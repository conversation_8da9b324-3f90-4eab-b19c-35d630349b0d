using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.ContractorManagement.ValueObjects;

/// <summary>
/// Value object representing contractor capabilities and specializations
/// </summary>
public class ContractorCapabilities : ValueObject
{
    public IReadOnlyList<string> Specializations { get; private set; } = new List<string>();
    public IReadOnlyList<string> ServiceTypes { get; private set; } = new List<string>();
    public IReadOnlyList<string> EquipmentTypes { get; private set; } = new List<string>();
    public decimal? MaxProjectValue { get; private set; }
    public string? MaxProjectValueCurrency { get; private set; }
    public int? MaxConcurrentProjects { get; private set; }
    public IReadOnlyList<string> GeographicAreas { get; private set; } = new List<string>();
    public IReadOnlyList<string> Certifications { get; private set; } = new List<string>();
    public int? YearsOfExperience { get; private set; }
    public int? NumberOfEmployees { get; private set; }
    public bool HasSafetyTraining { get; private set; }
    public bool HasInsurance { get; private set; }
    public bool HasBondingCapacity { get; private set; }
    public string? Notes { get; private set; }

    private ContractorCapabilities() { } // For EF Core

    public ContractorCapabilities(
        IEnumerable<string>? specializations = null,
        IEnumerable<string>? serviceTypes = null,
        IEnumerable<string>? equipmentTypes = null,
        decimal? maxProjectValue = null,
        string? maxProjectValueCurrency = null,
        int? maxConcurrentProjects = null,
        IEnumerable<string>? geographicAreas = null,
        IEnumerable<string>? certifications = null,
        int? yearsOfExperience = null,
        int? numberOfEmployees = null,
        bool hasSafetyTraining = false,
        bool hasInsurance = false,
        bool hasBondingCapacity = false,
        string? notes = null)
    {
        SetSpecializations(specializations);
        SetServiceTypes(serviceTypes);
        SetEquipmentTypes(equipmentTypes);
        SetMaxProjectValue(maxProjectValue, maxProjectValueCurrency);
        SetMaxConcurrentProjects(maxConcurrentProjects);
        SetGeographicAreas(geographicAreas);
        SetCertifications(certifications);
        SetYearsOfExperience(yearsOfExperience);
        SetNumberOfEmployees(numberOfEmployees);
        HasSafetyTraining = hasSafetyTraining;
        HasInsurance = hasInsurance;
        HasBondingCapacity = hasBondingCapacity;
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Creates capabilities with basic information
    /// </summary>
    public static ContractorCapabilities CreateBasic(
        IEnumerable<string> specializations,
        int? yearsOfExperience = null,
        bool hasInsurance = false)
    {
        return new ContractorCapabilities(
            specializations: specializations,
            yearsOfExperience: yearsOfExperience,
            hasInsurance: hasInsurance);
    }

    /// <summary>
    /// Creates comprehensive capabilities
    /// </summary>
    public static ContractorCapabilities CreateComprehensive(
        IEnumerable<string> specializations,
        IEnumerable<string> serviceTypes,
        decimal maxProjectValue,
        string currency,
        int maxConcurrentProjects,
        IEnumerable<string> geographicAreas,
        int yearsOfExperience,
        int numberOfEmployees,
        bool hasSafetyTraining = true,
        bool hasInsurance = true,
        bool hasBondingCapacity = true)
    {
        return new ContractorCapabilities(
            specializations: specializations,
            serviceTypes: serviceTypes,
            maxProjectValue: maxProjectValue,
            maxProjectValueCurrency: currency,
            maxConcurrentProjects: maxConcurrentProjects,
            geographicAreas: geographicAreas,
            yearsOfExperience: yearsOfExperience,
            numberOfEmployees: numberOfEmployees,
            hasSafetyTraining: hasSafetyTraining,
            hasInsurance: hasInsurance,
            hasBondingCapacity: hasBondingCapacity);
    }

    /// <summary>
    /// Checks if contractor can handle a specific project value
    /// </summary>
    public bool CanHandleProjectValue(decimal projectValue, string currency = "JPY")
    {
        if (!MaxProjectValue.HasValue)
            return true; // No limit specified

        if (MaxProjectValueCurrency != currency)
            return true; // Different currency, assume can handle

        return projectValue <= MaxProjectValue.Value;
    }

    /// <summary>
    /// Checks if contractor has a specific specialization
    /// </summary>
    public bool HasSpecialization(string specialization)
    {
        return Specializations.Any(s => string.Equals(s, specialization, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if contractor provides a specific service type
    /// </summary>
    public bool ProvidesServiceType(string serviceType)
    {
        return ServiceTypes.Any(s => string.Equals(s, serviceType, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if contractor operates in a specific geographic area
    /// </summary>
    public bool OperatesInArea(string area)
    {
        return GeographicAreas.Any(g => string.Equals(g, area, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Checks if contractor is qualified for complex projects
    /// </summary>
    public bool IsQualifiedForComplexProjects
    {
        get
        {
            return HasSafetyTraining &&
                   HasInsurance &&
                   HasBondingCapacity &&
                   YearsOfExperience >= 5 &&
                   NumberOfEmployees >= 10;
        }
    }

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        foreach (var specialization in Specializations)
            yield return specialization;
        foreach (var serviceType in ServiceTypes)
            yield return serviceType;
        foreach (var equipmentType in EquipmentTypes)
            yield return equipmentType;
        yield return MaxProjectValue;
        yield return MaxProjectValueCurrency;
        yield return MaxConcurrentProjects;
        foreach (var area in GeographicAreas)
            yield return area;
        foreach (var certification in Certifications)
            yield return certification;
        yield return YearsOfExperience;
        yield return NumberOfEmployees;
        yield return HasSafetyTraining;
        yield return HasInsurance;
        yield return HasBondingCapacity;
        yield return Notes;
    }

    // Private helper methods
    private void SetSpecializations(IEnumerable<string>? specializations)
    {
        var list = specializations?.Where(s => !string.IsNullOrWhiteSpace(s))
                                  .Select(s => s.Trim())
                                  .Distinct(StringComparer.OrdinalIgnoreCase)
                                  .ToList() ?? new List<string>();
        Specializations = list.AsReadOnly();
    }

    private void SetServiceTypes(IEnumerable<string>? serviceTypes)
    {
        var list = serviceTypes?.Where(s => !string.IsNullOrWhiteSpace(s))
                               .Select(s => s.Trim())
                               .Distinct(StringComparer.OrdinalIgnoreCase)
                               .ToList() ?? new List<string>();
        ServiceTypes = list.AsReadOnly();
    }

    private void SetEquipmentTypes(IEnumerable<string>? equipmentTypes)
    {
        var list = equipmentTypes?.Where(s => !string.IsNullOrWhiteSpace(s))
                                 .Select(s => s.Trim())
                                 .Distinct(StringComparer.OrdinalIgnoreCase)
                                 .ToList() ?? new List<string>();
        EquipmentTypes = list.AsReadOnly();
    }

    private void SetMaxProjectValue(decimal? maxProjectValue, string? currency)
    {
        if (maxProjectValue.HasValue && maxProjectValue.Value < 0)
            throw new ArgumentException("Max project value cannot be negative");

        MaxProjectValue = maxProjectValue;
        MaxProjectValueCurrency = currency?.Trim()?.ToUpperInvariant();
    }

    private void SetMaxConcurrentProjects(int? maxConcurrentProjects)
    {
        if (maxConcurrentProjects.HasValue && maxConcurrentProjects.Value < 1)
            throw new ArgumentException("Max concurrent projects must be at least 1");

        MaxConcurrentProjects = maxConcurrentProjects;
    }

    private void SetGeographicAreas(IEnumerable<string>? geographicAreas)
    {
        var list = geographicAreas?.Where(s => !string.IsNullOrWhiteSpace(s))
                                  .Select(s => s.Trim())
                                  .Distinct(StringComparer.OrdinalIgnoreCase)
                                  .ToList() ?? new List<string>();
        GeographicAreas = list.AsReadOnly();
    }

    private void SetCertifications(IEnumerable<string>? certifications)
    {
        var list = certifications?.Where(s => !string.IsNullOrWhiteSpace(s))
                                 .Select(s => s.Trim())
                                 .Distinct(StringComparer.OrdinalIgnoreCase)
                                 .ToList() ?? new List<string>();
        Certifications = list.AsReadOnly();
    }

    private void SetYearsOfExperience(int? yearsOfExperience)
    {
        if (yearsOfExperience.HasValue && yearsOfExperience.Value < 0)
            throw new ArgumentException("Years of experience cannot be negative");

        YearsOfExperience = yearsOfExperience;
    }

    private void SetNumberOfEmployees(int? numberOfEmployees)
    {
        if (numberOfEmployees.HasValue && numberOfEmployees.Value < 0)
            throw new ArgumentException("Number of employees cannot be negative");

        NumberOfEmployees = numberOfEmployees;
    }
}
