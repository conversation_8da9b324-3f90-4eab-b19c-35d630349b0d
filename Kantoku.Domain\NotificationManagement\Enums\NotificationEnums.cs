using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.NotificationManagement.Enums;

/// <summary>
/// Enumeration representing notification types
/// </summary>
public class NotificationType : Enumeration
{
    public static readonly NotificationType Info = new(1, nameof(Info), "Information", "info");
    public static readonly NotificationType Warning = new(2, nameof(Warning), "Warning", "warning");
    public static readonly NotificationType Error = new(3, nameof(Error), "Error", "error");
    public static readonly NotificationType Success = new(4, nameof(Success), "Success", "success");
    public static readonly NotificationType Reminder = new(5, nameof(Reminder), "Reminder", "reminder");
    public static readonly NotificationType Alert = new(6, nameof(Alert), "Alert", "alert");
    public static readonly NotificationType Announcement = new(7, nameof(Announcement), "Announcement", "announcement");

    public string DisplayName { get; private set; }
    public string IconClass { get; private set; }

    private NotificationType(int id, string name, string displayName, string iconClass) : base(id, name)
    {
        DisplayName = displayName;
        IconClass = iconClass;
    }

    public static IEnumerable<NotificationType> GetAll()
    {
        return new[] { Info, Warning, Error, Success, Reminder, Alert, Announcement };
    }

    public static NotificationType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown notification type: {name}");
        return type;
    }

    public static NotificationType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown notification type ID: {id}");
        return type;
    }
}

/// <summary>
/// Enumeration representing notification priorities
/// </summary>
public class NotificationPriority : Enumeration
{
    public static readonly NotificationPriority Low = new(1, nameof(Low), "Low", 1);
    public static readonly NotificationPriority Normal = new(2, nameof(Normal), "Normal", 2);
    public static readonly NotificationPriority High = new(3, nameof(High), "High", 3);
    public static readonly NotificationPriority Urgent = new(4, nameof(Urgent), "Urgent", 4);
    public static readonly NotificationPriority Critical = new(5, nameof(Critical), "Critical", 5);

    public string DisplayName { get; private set; }
    public int Level { get; private set; }

    private NotificationPriority(int id, string name, string displayName, int level) : base(id, name)
    {
        DisplayName = displayName;
        Level = level;
    }

    public static IEnumerable<NotificationPriority> GetAll()
    {
        return new[] { Low, Normal, High, Urgent, Critical };
    }

    public static NotificationPriority FromName(string name)
    {
        var priority = GetAll().FirstOrDefault(p => string.Equals(p.Name, name, StringComparison.OrdinalIgnoreCase));
        if (priority == null)
            throw new ArgumentException($"Unknown notification priority: {name}");
        return priority;
    }

    public static NotificationPriority FromId(int id)
    {
        var priority = GetAll().FirstOrDefault(p => p.Id == id);
        if (priority == null)
            throw new ArgumentException($"Unknown notification priority ID: {id}");
        return priority;
    }

    /// <summary>
    /// Checks if this priority is higher than another
    /// </summary>
    public bool IsHigherThan(NotificationPriority other)
    {
        return Level > other.Level;
    }

    /// <summary>
    /// Checks if this priority requires immediate attention
    /// </summary>
    public bool RequiresImmediateAttention => Level >= High.Level;
}

/// <summary>
/// Enumeration representing notification status
/// </summary>
public class NotificationStatus : Enumeration
{
    public static readonly NotificationStatus Draft = new(1, nameof(Draft), "Draft");
    public static readonly NotificationStatus PendingApproval = new(2, nameof(PendingApproval), "Pending Approval");
    public static readonly NotificationStatus Approved = new(3, nameof(Approved), "Approved");
    public static readonly NotificationStatus Rejected = new(4, nameof(Rejected), "Rejected");
    public static readonly NotificationStatus Scheduled = new(5, nameof(Scheduled), "Scheduled");
    public static readonly NotificationStatus Sent = new(6, nameof(Sent), "Sent");
    public static readonly NotificationStatus Cancelled = new(7, nameof(Cancelled), "Cancelled");

    public string DisplayName { get; private set; }

    private NotificationStatus(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<NotificationStatus> GetAll()
    {
        return new[] { Draft, PendingApproval, Approved, Rejected, Scheduled, Sent, Cancelled };
    }

    public static NotificationStatus FromName(string name)
    {
        var status = GetAll().FirstOrDefault(s => string.Equals(s.Name, name, StringComparison.OrdinalIgnoreCase));
        if (status == null)
            throw new ArgumentException($"Unknown notification status: {name}");
        return status;
    }

    public static NotificationStatus FromId(int id)
    {
        var status = GetAll().FirstOrDefault(s => s.Id == id);
        if (status == null)
            throw new ArgumentException($"Unknown notification status ID: {id}");
        return status;
    }

    /// <summary>
    /// Checks if the status allows editing
    /// </summary>
    public bool AllowsEditing => this == Draft || this == Rejected;

    /// <summary>
    /// Checks if the status is final
    /// </summary>
    public bool IsFinal => this == Sent || this == Cancelled;
}

/// <summary>
/// Enumeration representing target types
/// </summary>
public class TargetType : Enumeration
{
    public static readonly TargetType Employee = new(1, nameof(Employee), "Employee");
    public static readonly TargetType Role = new(2, nameof(Role), "Role");
    public static readonly TargetType Organization = new(3, nameof(Organization), "Organization");
    public static readonly TargetType Department = new(4, nameof(Department), "Department");
    public static readonly TargetType Project = new(5, nameof(Project), "Project");
    public static readonly TargetType Custom = new(6, nameof(Custom), "Custom");

    public string DisplayName { get; private set; }

    private TargetType(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<TargetType> GetAll()
    {
        return new[] { Employee, Role, Organization, Department, Project, Custom };
    }

    public static TargetType FromName(string name)
    {
        var type = GetAll().FirstOrDefault(t => string.Equals(t.Name, name, StringComparison.OrdinalIgnoreCase));
        if (type == null)
            throw new ArgumentException($"Unknown target type: {name}");
        return type;
    }

    public static TargetType FromId(int id)
    {
        var type = GetAll().FirstOrDefault(t => t.Id == id);
        if (type == null)
            throw new ArgumentException($"Unknown target type ID: {id}");
        return type;
    }
}

/// <summary>
/// Enumeration representing delivery channels
/// </summary>
public class DeliveryChannel : Enumeration
{
    public static readonly DeliveryChannel InApp = new(1, nameof(InApp), "In-App Notification");
    public static readonly DeliveryChannel Email = new(2, nameof(Email), "Email");
    public static readonly DeliveryChannel Sms = new(3, nameof(Sms), "SMS");
    public static readonly DeliveryChannel Push = new(4, nameof(Push), "Push Notification");
    public static readonly DeliveryChannel Webhook = new(5, nameof(Webhook), "Webhook");
    public static readonly DeliveryChannel Slack = new(6, nameof(Slack), "Slack");
    public static readonly DeliveryChannel Teams = new(7, nameof(Teams), "Microsoft Teams");

    public string DisplayName { get; private set; }

    private DeliveryChannel(int id, string name, string displayName) : base(id, name)
    {
        DisplayName = displayName;
    }

    public static IEnumerable<DeliveryChannel> GetAll()
    {
        return new[] { InApp, Email, Sms, Push, Webhook, Slack, Teams };
    }

    public static DeliveryChannel FromName(string name)
    {
        var channel = GetAll().FirstOrDefault(c => string.Equals(c.Name, name, StringComparison.OrdinalIgnoreCase));
        if (channel == null)
            throw new ArgumentException($"Unknown delivery channel: {name}");
        return channel;
    }

    public static DeliveryChannel FromId(int id)
    {
        var channel = GetAll().FirstOrDefault(c => c.Id == id);
        if (channel == null)
            throw new ArgumentException($"Unknown delivery channel ID: {id}");
        return channel;
    }

    /// <summary>
    /// Checks if the channel supports rich content
    /// </summary>
    public bool SupportsRichContent => this == InApp || this == Email || this == Slack || this == Teams;

    /// <summary>
    /// Checks if the channel supports attachments
    /// </summary>
    public bool SupportsAttachments => this == Email || this == Slack || this == Teams;

    /// <summary>
    /// Checks if the channel is real-time
    /// </summary>
    public bool IsRealTime => this == InApp || this == Push || this == Sms;
}
