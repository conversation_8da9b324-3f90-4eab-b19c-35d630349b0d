using System.Linq.Expressions;
using Kantoku.Domain.EmployeeManagement;
using Kantoku.Domain.EmployeeManagement.Enums;
using Kantoku.SharedKernel.Specifications;

namespace Kantoku.Domain.Specifications;

/// <summary>
/// Specification for active employees
/// </summary>
public class ActiveEmployeeSpecification : Specification<Employee>
{
    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.WorkingStatus == WorkingStatus.Active;
    }
}

/// <summary>
/// Specification for employees with approval authority
/// </summary>
public class EmployeeWithApprovalAuthoritySpecification : Specification<Employee>
{
    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.HasApprovalAuthority;
    }
}

/// <summary>
/// Specification for employees in a specific organization
/// </summary>
public class EmployeeInOrganizationSpecification : Specification<Employee>
{
    private readonly Guid _orgId;

    public EmployeeInOrganizationSpecification(Guid orgId)
    {
        _orgId = orgId;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.OrgId == _orgId;
    }
}

/// <summary>
/// Specification for employees with a specific working status
/// </summary>
public class EmployeeWithStatusSpecification : Specification<Employee>
{
    private readonly WorkingStatus _status;

    public EmployeeWithStatusSpecification(WorkingStatus status)
    {
        _status = status ?? throw new ArgumentNullException(nameof(status));
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.WorkingStatus == _status;
    }
}

/// <summary>
/// Specification for employees with a specific employee type
/// </summary>
public class EmployeeWithTypeSpecification : Specification<Employee>
{
    private readonly EmployeeType _employeeType;

    public EmployeeWithTypeSpecification(EmployeeType employeeType)
    {
        _employeeType = employeeType ?? throw new ArgumentNullException(nameof(employeeType));
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.EmployeeType == _employeeType;
    }
}

/// <summary>
/// Specification for employees in a specific structure
/// </summary>
public class EmployeeInStructureSpecification : Specification<Employee>
{
    private readonly Guid _structureId;

    public EmployeeInStructureSpecification(Guid structureId)
    {
        _structureId = structureId;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.StructureId == _structureId;
    }
}

/// <summary>
/// Specification for employees with a specific position
/// </summary>
public class EmployeeWithPositionSpecification : Specification<Employee>
{
    private readonly Guid _positionId;

    public EmployeeWithPositionSpecification(Guid positionId)
    {
        _positionId = positionId;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.PositionId == _positionId;
    }
}

/// <summary>
/// Specification for employees with a specific ranking
/// </summary>
public class EmployeeWithRankingSpecification : Specification<Employee>
{
    private readonly Guid _rankingId;

    public EmployeeWithRankingSpecification(Guid rankingId)
    {
        _rankingId = rankingId;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.RankingId == _rankingId;
    }
}

/// <summary>
/// Specification for employees with salary above a threshold
/// </summary>
public class EmployeeWithSalaryAboveSpecification : Specification<Employee>
{
    private readonly int _salaryThreshold;

    public EmployeeWithSalaryAboveSpecification(int salaryThreshold)
    {
        _salaryThreshold = salaryThreshold;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.SalaryInfo.SalaryInMonth >= _salaryThreshold;
    }
}

/// <summary>
/// Specification for employees who are organization admins
/// </summary>
public class OrganizationAdminEmployeeSpecification : Specification<Employee>
{
    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.IsOrgAdmin;
    }
}

/// <summary>
/// Specification for employees who are not hidden
/// </summary>
public class VisibleEmployeeSpecification : Specification<Employee>
{
    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => !employee.IsHidden;
    }
}

/// <summary>
/// Specification for employees hired within a date range
/// </summary>
public class EmployeeHiredInPeriodSpecification : Specification<Employee>
{
    private readonly DateTime _startDate;
    private readonly DateTime _endDate;

    public EmployeeHiredInPeriodSpecification(DateTime startDate, DateTime endDate)
    {
        _startDate = startDate;
        _endDate = endDate;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.WorkingFromDate.HasValue &&
                          employee.WorkingFromDate.Value >= _startDate &&
                          employee.WorkingFromDate.Value <= _endDate;
    }
}

/// <summary>
/// Specification for employees whose contracts are expiring
/// </summary>
public class EmployeeContractExpiringSpecification : Specification<Employee>
{
    private readonly DateTime _expiryThreshold;

    public EmployeeContractExpiringSpecification(int daysUntilExpiry = 30)
    {
        _expiryThreshold = DateTime.Today.AddDays(daysUntilExpiry);
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        return employee => employee.WorkingToDate.HasValue &&
                          employee.WorkingToDate.Value <= _expiryThreshold;
    }
}

/// <summary>
/// Specification for employees who can approve specific request types
/// </summary>
public class EmployeeCanApproveRequestSpecification : Specification<Employee>
{
    private readonly string _requestType;
    private readonly decimal? _requestAmount;

    public EmployeeCanApproveRequestSpecification(string requestType, decimal? requestAmount = null)
    {
        _requestType = requestType ?? throw new ArgumentNullException(nameof(requestType));
        _requestAmount = requestAmount;
    }

    public override Expression<Func<Employee, bool>> ToExpression()
    {
        // This is a simplified version - in practice, you might need to use a domain service
        // for complex approval logic
        return employee => employee.HasApprovalAuthority &&
                          employee.WorkingStatus == WorkingStatus.Active &&
                          (_requestType != "EQUIPMENT" && _requestType != "TRAVEL" || employee.IsOrgAdmin) &&
                          (_requestType != "EXPENSE" || !_requestAmount.HasValue || _requestAmount.Value <= 50000 || employee.IsOrgAdmin);
    }
}

/// <summary>
/// Composite specification for eligible employees (active, visible, with approval authority)
/// </summary>
public class EligibleEmployeeSpecification : Specification<Employee>
{
    public override Expression<Func<Employee, bool>> ToExpression()
    {
        var activeSpec = new ActiveEmployeeSpecification();
        var visibleSpec = new VisibleEmployeeSpecification();
        var approvalSpec = new EmployeeWithApprovalAuthoritySpecification();

        return activeSpec.And(visibleSpec).And(approvalSpec).ToExpression();
    }
}
