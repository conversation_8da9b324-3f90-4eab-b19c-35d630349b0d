using System.Linq.Expressions;
using Kantoku.SharedKernel.BuildingBlocks;
using Kantoku.SharedKernel.Specifications;

namespace Kantoku.Domain.Specifications;

/// <summary>
/// Extension methods for creating specifications with strongly-typed IDs
/// </summary>
public static class StronglyTypedIdSpecificationExtensions
{
    /// <summary>
    /// Creates a specification that matches entities by their strongly-typed ID
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TId">The strongly-typed ID type</typeparam>
    /// <param name="id">The strongly-typed ID to match</param>
    /// <param name="idSelector">Expression to select the ID property from the entity</param>
    /// <returns>A specification that matches entities with the specified ID</returns>
    public static ISpecification<TEntity> WithId<TEntity, TId>(
        TId id,
        Expression<Func<TEntity, Guid>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        if (id == null)
            throw new ArgumentNullException(nameof(id));
        if (idSelector == null)
            throw new ArgumentNullException(nameof(idSelector));

        return new ExpressionSpecification<TEntity>(
            Expression.Lambda<Func<TEntity, bool>>(
                Expression.Equal(idSelector.Body, Expression.Constant(id.Value)),
                idSelector.Parameters));
    }

    /// <summary>
    /// Creates a specification that matches entities by their strongly-typed ID (nullable)
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TId">The strongly-typed ID type</typeparam>
    /// <param name="id">The strongly-typed ID to match</param>
    /// <param name="idSelector">Expression to select the nullable ID property from the entity</param>
    /// <returns>A specification that matches entities with the specified ID</returns>
    public static ISpecification<TEntity> WithNullableId<TEntity, TId>(
        TId id,
        Expression<Func<TEntity, Guid?>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        if (id == null)
            throw new ArgumentNullException(nameof(id));
        if (idSelector == null)
            throw new ArgumentNullException(nameof(idSelector));

        return new ExpressionSpecification<TEntity>(
            Expression.Lambda<Func<TEntity, bool>>(
                Expression.Equal(idSelector.Body, Expression.Constant((Guid?)id.Value)),
                idSelector.Parameters));
    }

    /// <summary>
    /// Creates a specification that matches entities where the ID is in a collection of strongly-typed IDs
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TId">The strongly-typed ID type</typeparam>
    /// <param name="ids">The collection of strongly-typed IDs to match</param>
    /// <param name="idSelector">Expression to select the ID property from the entity</param>
    /// <returns>A specification that matches entities with IDs in the specified collection</returns>
    public static ISpecification<TEntity> WithIdIn<TEntity, TId>(
        IEnumerable<TId> ids,
        Expression<Func<TEntity, Guid>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        if (ids == null)
            throw new ArgumentNullException(nameof(ids));
        if (idSelector == null)
            throw new ArgumentNullException(nameof(idSelector));

        var guidIds = ids.Select(id => id.Value).ToList();
        if (!guidIds.Any())
            return new FalseSpecification<TEntity>();

        return new ExpressionSpecification<TEntity>(
            Expression.Lambda<Func<TEntity, bool>>(
                Expression.Call(
                    typeof(Enumerable),
                    nameof(Enumerable.Contains),
                    new[] { typeof(Guid) },
                    Expression.Constant(guidIds),
                    idSelector.Body),
                idSelector.Parameters));
    }

    /// <summary>
    /// Creates a specification that matches entities where the nullable ID is in a collection of strongly-typed IDs
    /// </summary>
    /// <typeparam name="TEntity">The entity type</typeparam>
    /// <typeparam name="TId">The strongly-typed ID type</typeparam>
    /// <param name="ids">The collection of strongly-typed IDs to match</param>
    /// <param name="idSelector">Expression to select the nullable ID property from the entity</param>
    /// <returns>A specification that matches entities with IDs in the specified collection</returns>
    public static ISpecification<TEntity> WithNullableIdIn<TEntity, TId>(
        IEnumerable<TId> ids,
        Expression<Func<TEntity, Guid?>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        if (ids == null)
            throw new ArgumentNullException(nameof(ids));
        if (idSelector == null)
            throw new ArgumentNullException(nameof(idSelector));

        var guidIds = ids.Select(id => (Guid?)id.Value).ToList();
        if (!guidIds.Any())
            return new FalseSpecification<TEntity>();

        return new ExpressionSpecification<TEntity>(
            Expression.Lambda<Func<TEntity, bool>>(
                Expression.Call(
                    typeof(Enumerable),
                    nameof(Enumerable.Contains),
                    new[] { typeof(Guid?) },
                    Expression.Constant(guidIds),
                    idSelector.Body),
                idSelector.Parameters));
    }
}

/// <summary>
/// Specification builder for creating complex specifications with strongly-typed IDs
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
public class StronglyTypedIdSpecificationBuilder<TEntity>
{
    private ISpecification<TEntity> _specification = new TrueSpecification<TEntity>();

    /// <summary>
    /// Adds a condition that the entity ID matches the specified strongly-typed ID
    /// </summary>
    public StronglyTypedIdSpecificationBuilder<TEntity> WithId<TId>(
        TId id,
        Expression<Func<TEntity, Guid>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        var spec = StronglyTypedIdSpecificationExtensions.WithId<TEntity, TId>(id, idSelector);
        _specification = _specification.And(spec);
        return this;
    }

    /// <summary>
    /// Adds a condition that the entity nullable ID matches the specified strongly-typed ID
    /// </summary>
    public StronglyTypedIdSpecificationBuilder<TEntity> WithNullableId<TId>(
        TId id,
        Expression<Func<TEntity, Guid?>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        var spec = StronglyTypedIdSpecificationExtensions.WithNullableId<TEntity, TId>(id, idSelector);
        _specification = _specification.And(spec);
        return this;
    }

    /// <summary>
    /// Adds a condition that the entity ID is in the specified collection of strongly-typed IDs
    /// </summary>
    public StronglyTypedIdSpecificationBuilder<TEntity> WithIdIn<TId>(
        IEnumerable<TId> ids,
        Expression<Func<TEntity, Guid>> idSelector)
        where TId : StronglyTypedId<TId>
    {
        var spec = StronglyTypedIdSpecificationExtensions.WithIdIn<TEntity, TId>(ids, idSelector);
        _specification = _specification.And(spec);
        return this;
    }

    /// <summary>
    /// Adds a custom specification
    /// </summary>
    public StronglyTypedIdSpecificationBuilder<TEntity> And(ISpecification<TEntity> specification)
    {
        _specification = _specification.And(specification);
        return this;
    }

    /// <summary>
    /// Builds the final specification
    /// </summary>
    public ISpecification<TEntity> Build() => _specification;

    /// <summary>
    /// Implicit conversion to specification
    /// </summary>
    public static implicit operator Specification<TEntity>(StronglyTypedIdSpecificationBuilder<TEntity> builder)
    {
        return (Specification<TEntity>)builder.Build();
    }
}
