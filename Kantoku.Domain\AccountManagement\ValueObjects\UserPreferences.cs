using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.AccountManagement.ValueObjects;

/// <summary>
/// Value object representing user preferences
/// </summary>
public class UserPreferences : ValueObject
{
    public string Theme { get; private set; } = "light"; // light, dark, auto
    public string Language { get; private set; } = "en";
    public string TimeZone { get; private set; } = "UTC";
    public string DateFormat { get; private set; } = "yyyy-MM-dd";
    public string TimeFormat { get; private set; } = "HH:mm";
    public string Currency { get; private set; } = "JPY";
    public bool EmailNotifications { get; private set; } = true;
    public bool PushNotifications { get; private set; } = true;
    public bool SmsNotifications { get; private set; } = false;
    public int ItemsPerPage { get; private set; } = 20;
    public bool ShowTutorials { get; private set; } = true;
    public bool AutoSave { get; private set; } = true;
    public Dictionary<string, object> CustomSettings { get; private set; } = new();

    private UserPreferences() { } // For EF Core

    public UserPreferences(
        string? theme = null,
        string? language = null,
        string? timeZone = null,
        string? dateFormat = null,
        string? timeFormat = null,
        string? currency = null,
        bool emailNotifications = true,
        bool pushNotifications = true,
        bool smsNotifications = false,
        int itemsPerPage = 20,
        bool showTutorials = true,
        bool autoSave = true,
        Dictionary<string, object>? customSettings = null)
    {
        SetTheme(theme ?? "light");
        SetLanguage(language ?? "en");
        SetTimeZone(timeZone ?? "UTC");
        SetDateFormat(dateFormat ?? "yyyy-MM-dd");
        SetTimeFormat(timeFormat ?? "HH:mm");
        SetCurrency(currency ?? "JPY");
        EmailNotifications = emailNotifications;
        PushNotifications = pushNotifications;
        SmsNotifications = smsNotifications;
        SetItemsPerPage(itemsPerPage);
        ShowTutorials = showTutorials;
        AutoSave = autoSave;
        CustomSettings = customSettings ?? new Dictionary<string, object>();
    }

    /// <summary>
    /// Creates default preferences
    /// </summary>
    public static UserPreferences CreateDefault()
    {
        return new UserPreferences();
    }

    /// <summary>
    /// Creates preferences for Japanese users
    /// </summary>
    public static UserPreferences CreateForJapanese()
    {
        return new UserPreferences(
            language: "ja",
            timeZone: "Asia/Tokyo",
            currency: "JPY");
    }

    /// <summary>
    /// Updates notification preferences
    /// </summary>
    public UserPreferences UpdateNotifications(
        bool emailNotifications,
        bool pushNotifications,
        bool smsNotifications)
    {
        return new UserPreferences(
            Theme, Language, TimeZone, DateFormat, TimeFormat, Currency,
            emailNotifications, pushNotifications, smsNotifications,
            ItemsPerPage, ShowTutorials, AutoSave, CustomSettings);
    }

    /// <summary>
    /// Updates display preferences
    /// </summary>
    public UserPreferences UpdateDisplay(
        string theme,
        string language,
        string timeZone,
        string dateFormat,
        string timeFormat,
        int itemsPerPage)
    {
        return new UserPreferences(
            theme, language, timeZone, dateFormat, timeFormat, Currency,
            EmailNotifications, PushNotifications, SmsNotifications,
            itemsPerPage, ShowTutorials, AutoSave, CustomSettings);
    }

    /// <summary>
    /// Updates a custom setting
    /// </summary>
    public UserPreferences UpdateCustomSetting(string key, object value)
    {
        var newCustomSettings = new Dictionary<string, object>(CustomSettings)
        {
            [key] = value
        };

        return new UserPreferences(
            Theme, Language, TimeZone, DateFormat, TimeFormat, Currency,
            EmailNotifications, PushNotifications, SmsNotifications,
            ItemsPerPage, ShowTutorials, AutoSave, newCustomSettings);
    }

    /// <summary>
    /// Removes a custom setting
    /// </summary>
    public UserPreferences RemoveCustomSetting(string key)
    {
        var newCustomSettings = new Dictionary<string, object>(CustomSettings);
        newCustomSettings.Remove(key);

        return new UserPreferences(
            Theme, Language, TimeZone, DateFormat, TimeFormat, Currency,
            EmailNotifications, PushNotifications, SmsNotifications,
            ItemsPerPage, ShowTutorials, AutoSave, newCustomSettings);
    }

    /// <summary>
    /// Gets a custom setting value
    /// </summary>
    public T? GetCustomSetting<T>(string key, T? defaultValue = default)
    {
        if (CustomSettings.TryGetValue(key, out var value) && value is T typedValue)
            return typedValue;

        return defaultValue;
    }

    /// <summary>
    /// Checks if notifications are enabled
    /// </summary>
    public bool HasNotificationsEnabled => EmailNotifications || PushNotifications || SmsNotifications;

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return Theme;
        yield return Language;
        yield return TimeZone;
        yield return DateFormat;
        yield return TimeFormat;
        yield return Currency;
        yield return EmailNotifications;
        yield return PushNotifications;
        yield return SmsNotifications;
        yield return ItemsPerPage;
        yield return ShowTutorials;
        yield return AutoSave;

        foreach (var kvp in CustomSettings.OrderBy(x => x.Key))
        {
            yield return kvp.Key;
            yield return kvp.Value;
        }
    }

    // Private helper methods
    private void SetTheme(string theme)
    {
        var validThemes = new[] { "light", "dark", "auto" };
        if (!validThemes.Contains(theme.ToLowerInvariant()))
            throw new ArgumentException($"Invalid theme. Valid themes are: {string.Join(", ", validThemes)}");

        Theme = theme.ToLowerInvariant();
    }

    private void SetLanguage(string language)
    {
        if (string.IsNullOrWhiteSpace(language))
            throw new ArgumentException("Language cannot be null or empty");

        if (language.Length > 10)
            throw new ArgumentException("Language code cannot exceed 10 characters");

        Language = language.ToLowerInvariant();
    }

    private void SetTimeZone(string timeZone)
    {
        if (string.IsNullOrWhiteSpace(timeZone))
            throw new ArgumentException("Time zone cannot be null or empty");

        // Basic validation - in a real system, you'd validate against known time zones
        TimeZone = timeZone;
    }

    private void SetDateFormat(string dateFormat)
    {
        if (string.IsNullOrWhiteSpace(dateFormat))
            throw new ArgumentException("Date format cannot be null or empty");

        DateFormat = dateFormat;
    }

    private void SetTimeFormat(string timeFormat)
    {
        if (string.IsNullOrWhiteSpace(timeFormat))
            throw new ArgumentException("Time format cannot be null or empty");

        TimeFormat = timeFormat;
    }

    private void SetCurrency(string currency)
    {
        if (string.IsNullOrWhiteSpace(currency))
            throw new ArgumentException("Currency cannot be null or empty");

        if (currency.Length != 3)
            throw new ArgumentException("Currency must be a 3-character ISO code");

        Currency = currency.ToUpperInvariant();
    }

    private void SetItemsPerPage(int itemsPerPage)
    {
        if (itemsPerPage < 5 || itemsPerPage > 100)
            throw new ArgumentException("Items per page must be between 5 and 100");

        ItemsPerPage = itemsPerPage;
    }
}
