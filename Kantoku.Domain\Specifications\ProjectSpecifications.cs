using System.Linq.Expressions;
using Kantoku.Domain.ProjectManagement;
using Kantoku.Domain.ProjectManagement.Enums;
using Kantoku.Domain.OrganizationManagement;
using Kantoku.Domain.CustomerManagement;
using Kantoku.Domain.ContractorManagement;
using Kantoku.SharedKernel.Specifications;

namespace Kantoku.Domain.Specifications;

/// <summary>
/// Specification for active projects
/// </summary>
public class ActiveProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Status == ProjectStatus.InProgress;
    }
}

/// <summary>
/// Specification for projects in a specific organization
/// </summary>
public class ProjectInOrganizationSpecification : Specification<Project>
{
    private readonly OrganizationId _orgId;

    public ProjectInOrganizationSpecification(OrganizationId orgId)
    {
        _orgId = orgId ?? throw new ArgumentNullException(nameof(orgId));
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.OrgId == _orgId.Value;
    }
}

/// <summary>
/// Specification for projects in a specific organization (using Guid for backward compatibility)
/// </summary>
public class ProjectInOrganizationByGuidSpecification : Specification<Project>
{
    private readonly Guid _orgId;

    public ProjectInOrganizationByGuidSpecification(Guid orgId)
    {
        _orgId = orgId;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.OrgId == _orgId;
    }
}

/// <summary>
/// Specification for projects with a specific status
/// </summary>
public class ProjectWithStatusSpecification : Specification<Project>
{
    private readonly ProjectStatus _status;

    public ProjectWithStatusSpecification(ProjectStatus status)
    {
        _status = status ?? throw new ArgumentNullException(nameof(status));
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Status == _status;
    }
}

/// <summary>
/// Specification for projects assigned to a specific customer
/// </summary>
public class ProjectForCustomerSpecification : Specification<Project>
{
    private readonly CustomerId _customerId;

    public ProjectForCustomerSpecification(CustomerId customerId)
    {
        _customerId = customerId ?? throw new ArgumentNullException(nameof(customerId));
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.CustomerId == _customerId.Value;
    }
}

/// <summary>
/// Specification for projects assigned to a specific customer (using Guid for backward compatibility)
/// </summary>
public class ProjectForCustomerByGuidSpecification : Specification<Project>
{
    private readonly Guid _customerId;

    public ProjectForCustomerByGuidSpecification(Guid customerId)
    {
        _customerId = customerId;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.CustomerId == _customerId;
    }
}

/// <summary>
/// Specification for projects assigned to a specific contractor
/// </summary>
public class ProjectForContractorSpecification : Specification<Project>
{
    private readonly ContractorId _contractorId;

    public ProjectForContractorSpecification(ContractorId contractorId)
    {
        _contractorId = contractorId ?? throw new ArgumentNullException(nameof(contractorId));
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.ContractorId == _contractorId.Value;
    }
}

/// <summary>
/// Specification for projects assigned to a specific contractor (using Guid for backward compatibility)
/// </summary>
public class ProjectForContractorByGuidSpecification : Specification<Project>
{
    private readonly Guid _contractorId;

    public ProjectForContractorByGuidSpecification(Guid contractorId)
    {
        _contractorId = contractorId;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.ContractorId == _contractorId;
    }
}

/// <summary>
/// Specification for projects with budget above a threshold
/// </summary>
public class ProjectWithBudgetAboveSpecification : Specification<Project>
{
    private readonly decimal _budgetThreshold;

    public ProjectWithBudgetAboveSpecification(decimal budgetThreshold)
    {
        _budgetThreshold = budgetThreshold;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Budget != null && project.Budget.EstimatedCost >= _budgetThreshold;
    }
}

/// <summary>
/// Specification for projects starting within a date range
/// </summary>
public class ProjectStartingInPeriodSpecification : Specification<Project>
{
    private readonly DateOnly _startDate;
    private readonly DateOnly _endDate;

    public ProjectStartingInPeriodSpecification(DateOnly startDate, DateOnly endDate)
    {
        _startDate = startDate;
        _endDate = endDate;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Dates != null &&
                          project.Dates.ExpectedStartDate >= _startDate &&
                          project.Dates.ExpectedStartDate <= _endDate;
    }
}

/// <summary>
/// Specification for projects ending within a date range
/// </summary>
public class ProjectEndingInPeriodSpecification : Specification<Project>
{
    private readonly DateOnly _startDate;
    private readonly DateOnly _endDate;

    public ProjectEndingInPeriodSpecification(DateOnly startDate, DateOnly endDate)
    {
        _startDate = startDate;
        _endDate = endDate;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Dates != null &&
                          project.Dates.ExpectedEndDate >= _startDate &&
                          project.Dates.ExpectedEndDate <= _endDate;
    }
}

/// <summary>
/// Specification for overdue projects
/// </summary>
public class OverdueProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        var today = DateOnly.FromDateTime(DateTime.Today);
        return project => project.Dates != null &&
                          project.Dates.ExpectedEndDate < today &&
                          (project.Status == ProjectStatus.InProgress || project.Status == ProjectStatus.Planning);
    }
}

/// <summary>
/// Specification for projects that are behind schedule
/// </summary>
public class BehindScheduleProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        var today = DateOnly.FromDateTime(DateTime.Today);
        return project => project.Dates != null &&
                          project.Dates.ActualStartDate.HasValue &&
                          project.Dates.ActualStartDate.Value > project.Dates.ExpectedStartDate;
    }
}

/// <summary>
/// Specification for projects over budget
/// </summary>
public class OverBudgetProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Budget != null &&
                          project.Budget.ActualCost > project.Budget.EstimatedCost;
    }
}

/// <summary>
/// Specification for office projects
/// </summary>
public class OfficeProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.IsOffice;
    }
}

/// <summary>
/// Specification for default projects
/// </summary>
public class DefaultProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.IsDefault;
    }
}

/// <summary>
/// Specification for projects with a specific project type
/// </summary>
public class ProjectWithTypeSpecification : Specification<Project>
{
    private readonly ProjectTypeId _projectTypeId;

    public ProjectWithTypeSpecification(ProjectTypeId projectTypeId)
    {
        _projectTypeId = projectTypeId ?? throw new ArgumentNullException(nameof(projectTypeId));
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.ProjectTypeId == _projectTypeId.Value;
    }
}

/// <summary>
/// Specification for projects with a specific project type (using Guid for backward compatibility)
/// </summary>
public class ProjectWithTypeByGuidSpecification : Specification<Project>
{
    private readonly Guid _projectTypeId;

    public ProjectWithTypeByGuidSpecification(Guid projectTypeId)
    {
        _projectTypeId = projectTypeId;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.ProjectTypeId == _projectTypeId;
    }
}

/// <summary>
/// Specification for projects requiring attention (overdue or over budget)
/// </summary>
public class ProjectRequiringAttentionSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        var overdueSpec = new OverdueProjectSpecification();
        var overBudgetSpec = new OverBudgetProjectSpecification();

        return overdueSpec.Or(overBudgetSpec).ToExpression();
    }
}

/// <summary>
/// Specification for projects in critical status (active and requiring attention)
/// </summary>
public class CriticalProjectSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        var activeSpec = new ActiveProjectSpecification();
        var attentionSpec = new ProjectRequiringAttentionSpecification();

        return activeSpec.And(attentionSpec).ToExpression();
    }
}

/// <summary>
/// Specification for projects with monthly report date in current month
/// </summary>
public class ProjectWithCurrentMonthReportSpecification : Specification<Project>
{
    public override Expression<Func<Project, bool>> ToExpression()
    {
        var currentDay = DateTime.Today.Day;
        return project => project.MonthlyReportDate.HasValue &&
                          project.MonthlyReportDate.Value <= currentDay;
    }
}

/// <summary>
/// Specification for large projects (budget above threshold)
/// </summary>
public class LargeProjectSpecification : Specification<Project>
{
    private readonly decimal _largeProjectThreshold;

    public LargeProjectSpecification(decimal largeProjectThreshold = 10000000) // 10M yen default
    {
        _largeProjectThreshold = largeProjectThreshold;
    }

    public override Expression<Func<Project, bool>> ToExpression()
    {
        return project => project.Budget != null &&
                          project.Budget.EstimatedCost >= _largeProjectThreshold;
    }
}
