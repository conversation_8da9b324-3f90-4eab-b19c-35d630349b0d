using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.EmployeeAggregate;

public class WorkingStatusEnum : Enumeration<WorkingStatusEnum, int>
{
    public static readonly WorkingStatusEnum INVITED = new(1, nameof(INVITED));
    public static readonly WorkingStatusEnum ONBOARDED = new(2, nameof(ONBOARDED));
    public static readonly WorkingStatusEnum WORKING = new(3, nameof(WORKING));
    public static readonly WorkingStatusEnum EXTENT_LEAVE = new(4, nameof(EXTENT_LEAVE));
    public static readonly WorkingStatusEnum RETIRED = new(5, nameof(RETIRED));

    private WorkingStatusEnum(int value, string name) : base(value, name)
    {
    }
}
