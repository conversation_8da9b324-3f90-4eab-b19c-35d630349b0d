using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.AccountManagement;

/// <summary>
/// Entity representing a role assignment to a user
/// </summary>
public class UserRole : Entity<Guid>
{
    public Guid AccountId { get; private set; }
    public Guid RoleId { get; private set; }
    public Guid? OrgId { get; private set; } // Role can be organization-specific
    public DateTime AssignedDate { get; private set; }
    public DateTime? RevokedDate { get; private set; }
    public Guid AssignedBy { get; private set; }
    public Guid? RevokedBy { get; private set; }
    public bool IsActive { get; private set; } = true;
    public string? Notes { get; private set; }

    // Private constructor for EF Core
    private UserRole() : base() { }

    /// <summary>
    /// Creates a new user role assignment
    /// </summary>
    public UserRole(
        Guid id,
        Guid accountId,
        Guid roleId,
        Guid assignedBy,
        Guid? orgId = null,
        DateTime? assignedDate = null,
        string? notes = null) : base(id)
    {
        AccountId = accountId;
        RoleId = roleId;
        AssignedBy = assignedBy;
        OrgId = orgId;
        AssignedDate = assignedDate ?? DateTime.UtcNow;
        Notes = notes?.Trim();
    }

    /// <summary>
    /// Revokes the role assignment
    /// </summary>
    public void Revoke(Guid revokedBy, string? reason = null)
    {
        if (!IsActive)
            throw new InvalidOperationException("Role assignment is already revoked");

        IsActive = false;
        RevokedDate = DateTime.UtcNow;
        RevokedBy = revokedBy;
        Notes = string.IsNullOrWhiteSpace(reason) ? Notes : $"{Notes} | Revoked: {reason}".Trim('|', ' ');
    }

    /// <summary>
    /// Reactivates the role assignment
    /// </summary>
    public void Reactivate(Guid reactivatedBy, string? reason = null)
    {
        if (IsActive)
            throw new InvalidOperationException("Role assignment is already active");

        IsActive = true;
        RevokedDate = null;
        RevokedBy = null;
        AssignedBy = reactivatedBy; // Update who reactivated it
        Notes = string.IsNullOrWhiteSpace(reason) ? Notes : $"{Notes} | Reactivated: {reason}".Trim('|', ' ');
    }

    /// <summary>
    /// Checks if the role assignment is currently valid
    /// </summary>
    public bool IsValid => IsActive && !RevokedDate.HasValue;

    /// <summary>
    /// Gets the duration of the role assignment
    /// </summary>
    public TimeSpan Duration
    {
        get
        {
            var endDate = RevokedDate ?? DateTime.UtcNow;
            return endDate - AssignedDate;
        }
    }
}

/// <summary>
/// Entity representing a role in the system
/// </summary>
public class Role : Entity<Guid>
{
    private readonly List<RoleFunction> _roleFunctions = new();

    public string RoleName { get; private set; } = null!;
    public string? Description { get; private set; }
    public bool IsSystemRole { get; private set; } = false;
    public bool IsActive { get; private set; } = true;
    public Guid? OrgId { get; private set; } // Organization-specific role
    public int Priority { get; private set; } = 0; // Higher number = higher priority

    // Navigation properties (read-only collections)
    public IReadOnlyCollection<RoleFunction> RoleFunctions => _roleFunctions.AsReadOnly();

    // Private constructor for EF Core
    private Role() : base() { }

    /// <summary>
    /// Creates a new role
    /// </summary>
    public Role(
        Guid id,
        string roleName,
        string? description = null,
        bool isSystemRole = false,
        Guid? orgId = null,
        int priority = 0) : base(id)
    {
        SetRoleName(roleName);
        Description = description?.Trim();
        IsSystemRole = isSystemRole;
        OrgId = orgId;
        Priority = priority;
    }

    /// <summary>
    /// Updates role information
    /// </summary>
    public void UpdateInfo(string roleName, string? description = null, int priority = 0)
    {
        if (IsSystemRole)
            throw new InvalidOperationException("Cannot modify system roles");

        SetRoleName(roleName);
        Description = description?.Trim();
        Priority = priority;
    }

    /// <summary>
    /// Adds a function to the role
    /// </summary>
    public void AddFunction(RoleFunction roleFunction)
    {
        if (roleFunction == null)
            throw new ArgumentNullException(nameof(roleFunction));

        if (_roleFunctions.Any(rf => rf.FunctionId == roleFunction.FunctionId))
            throw new InvalidOperationException("Function already assigned to this role");

        _roleFunctions.Add(roleFunction);
    }

    /// <summary>
    /// Removes a function from the role
    /// </summary>
    public void RemoveFunction(Guid functionId)
    {
        var roleFunction = _roleFunctions.FirstOrDefault(rf => rf.FunctionId == functionId);
        if (roleFunction != null)
        {
            _roleFunctions.Remove(roleFunction);
        }
    }

    /// <summary>
    /// Activates the role
    /// </summary>
    public void Activate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Deactivates the role
    /// </summary>
    public void Deactivate()
    {
        if (IsSystemRole)
            throw new InvalidOperationException("Cannot deactivate system roles");

        IsActive = false;
    }

    /// <summary>
    /// Checks if the role has a specific function
    /// </summary>
    public bool HasFunction(Guid functionId)
    {
        return _roleFunctions.Any(rf => rf.FunctionId == functionId && rf.IsActive);
    }

    /// <summary>
    /// Gets all active function IDs for this role
    /// </summary>
    public IEnumerable<Guid> GetActiveFunctionIds()
    {
        return _roleFunctions.Where(rf => rf.IsActive).Select(rf => rf.FunctionId);
    }

    // Private helper methods
    private void SetRoleName(string roleName)
    {
        if (string.IsNullOrWhiteSpace(roleName))
            throw new ArgumentException("Role name cannot be null or empty", nameof(roleName));

        if (roleName.Length > 100)
            throw new ArgumentException("Role name cannot exceed 100 characters", nameof(roleName));

        RoleName = roleName.Trim();
    }
}

/// <summary>
/// Entity representing a function assignment to a role
/// </summary>
public class RoleFunction : Entity<Guid>
{
    public Guid RoleId { get; private set; }
    public Guid FunctionId { get; private set; }
    public DateTime AssignedDate { get; private set; }
    public DateTime? RevokedDate { get; private set; }
    public bool IsActive { get; private set; } = true;

    // Private constructor for EF Core
    private RoleFunction() : base() { }

    /// <summary>
    /// Creates a new role function assignment
    /// </summary>
    public RoleFunction(
        Guid id,
        Guid roleId,
        Guid functionId,
        DateTime? assignedDate = null) : base(id)
    {
        RoleId = roleId;
        FunctionId = functionId;
        AssignedDate = assignedDate ?? DateTime.UtcNow;
    }

    /// <summary>
    /// Revokes the function assignment
    /// </summary>
    public void Revoke()
    {
        IsActive = false;
        RevokedDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Reactivates the function assignment
    /// </summary>
    public void Reactivate()
    {
        IsActive = true;
        RevokedDate = null;
    }
}

/// <summary>
/// Entity representing a function/permission in the system
/// </summary>
public class Function : Entity<Guid>
{
    public string FunctionName { get; private set; } = null!;
    public string? Description { get; private set; }
    public string FunctionCode { get; private set; } = null!;
    public string Category { get; private set; } = null!;
    public bool IsSystemFunction { get; private set; } = false;
    public bool IsActive { get; private set; } = true;

    // Private constructor for EF Core
    private Function() : base() { }

    /// <summary>
    /// Creates a new function
    /// </summary>
    public Function(
        Guid id,
        string functionName,
        string functionCode,
        string category,
        string? description = null,
        bool isSystemFunction = false) : base(id)
    {
        SetFunctionName(functionName);
        SetFunctionCode(functionCode);
        SetCategory(category);
        Description = description?.Trim();
        IsSystemFunction = isSystemFunction;
    }

    /// <summary>
    /// Updates function information
    /// </summary>
    public void UpdateInfo(string functionName, string? description = null)
    {
        if (IsSystemFunction)
            throw new InvalidOperationException("Cannot modify system functions");

        SetFunctionName(functionName);
        Description = description?.Trim();
    }

    /// <summary>
    /// Activates the function
    /// </summary>
    public void Activate()
    {
        IsActive = true;
    }

    /// <summary>
    /// Deactivates the function
    /// </summary>
    public void Deactivate()
    {
        if (IsSystemFunction)
            throw new InvalidOperationException("Cannot deactivate system functions");

        IsActive = false;
    }

    // Private helper methods
    private void SetFunctionName(string functionName)
    {
        if (string.IsNullOrWhiteSpace(functionName))
            throw new ArgumentException("Function name cannot be null or empty", nameof(functionName));

        if (functionName.Length > 100)
            throw new ArgumentException("Function name cannot exceed 100 characters", nameof(functionName));

        FunctionName = functionName.Trim();
    }

    private void SetFunctionCode(string functionCode)
    {
        if (string.IsNullOrWhiteSpace(functionCode))
            throw new ArgumentException("Function code cannot be null or empty", nameof(functionCode));

        if (functionCode.Length > 50)
            throw new ArgumentException("Function code cannot exceed 50 characters", nameof(functionCode));

        FunctionCode = functionCode.Trim().ToUpperInvariant();
    }

    private void SetCategory(string category)
    {
        if (string.IsNullOrWhiteSpace(category))
            throw new ArgumentException("Category cannot be null or empty", nameof(category));

        if (category.Length > 50)
            throw new ArgumentException("Category cannot exceed 50 characters", nameof(category));

        Category = category.Trim();
    }
}
