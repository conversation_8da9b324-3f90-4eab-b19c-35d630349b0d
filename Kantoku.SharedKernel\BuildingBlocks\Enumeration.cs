using System.Reflection;

namespace Kantoku.SharedKernel.BuildingBlocks;

/// <summary>
/// Base class for creating type-safe enumerations.
/// Enumerations provide a way to create strongly-typed constants with behavior,
/// which is more powerful than traditional enums.
/// </summary>
public abstract class Enumeration : IComparable
{
    /// <summary>
    /// Gets the unique identifier for this enumeration value
    /// </summary>
    public int Id { get; private set; }

    /// <summary>
    /// Gets the name of this enumeration value
    /// </summary>
    public string Name { get; private set; }

    /// <summary>
    /// Initializes a new instance of the Enumeration class
    /// </summary>
    /// <param name="id">The unique identifier</param>
    /// <param name="name">The name</param>
    protected Enumeration(int id, string name)
    {
        Id = id;
        Name = name;
    }

    /// <summary>
    /// Returns the string representation of the enumeration
    /// </summary>
    /// <returns>The name of the enumeration</returns>
    public override string ToString() => Name;

    /// <summary>
    /// Gets all enumeration values of the specified type
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <returns>All enumeration values</returns>
    public static IEnumerable<T> GetAll<T>() where T : Enumeration
    {
        var fields = typeof(T).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.DeclaredOnly);

        return fields
            .Select(f => f.GetValue(null))
            .Cast<T>();
    }

    /// <summary>
    /// Gets an enumeration value by its identifier
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <param name="id">The identifier</param>
    /// <returns>The enumeration value</returns>
    /// <exception cref="InvalidOperationException">Thrown when no enumeration with the specified ID is found</exception>
    public static T FromId<T>(int id) where T : Enumeration
    {
        var matchingItem = Parse<T, int>(id, "id", item => item.Id == id);
        return matchingItem;
    }

    /// <summary>
    /// Gets an enumeration value by its name
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <param name="name">The name</param>
    /// <returns>The enumeration value</returns>
    /// <exception cref="InvalidOperationException">Thrown when no enumeration with the specified name is found</exception>
    public static T FromName<T>(string name) where T : Enumeration
    {
        var matchingItem = Parse<T, string>(name, "name", item => item.Name == name);
        return matchingItem;
    }

    /// <summary>
    /// Tries to get an enumeration value by its identifier
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <param name="id">The identifier</param>
    /// <param name="result">The enumeration value if found</param>
    /// <returns>True if the enumeration was found; otherwise, false</returns>
    public static bool TryFromId<T>(int id, out T? result) where T : Enumeration
    {
        result = GetAll<T>().FirstOrDefault(item => item.Id == id);
        return result != null;
    }

    /// <summary>
    /// Tries to get an enumeration value by its name
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <param name="name">The name</param>
    /// <param name="result">The enumeration value if found</param>
    /// <returns>True if the enumeration was found; otherwise, false</returns>
    public static bool TryFromName<T>(string name, out T? result) where T : Enumeration
    {
        result = GetAll<T>().FirstOrDefault(item => string.Equals(item.Name, name, StringComparison.OrdinalIgnoreCase));
        return result != null;
    }

    /// <summary>
    /// Determines whether this enumeration is equal to another object
    /// </summary>
    /// <param name="obj">The other object</param>
    /// <returns>True if the objects are equal; otherwise, false</returns>
    public override bool Equals(object? obj)
    {
        if (obj is not Enumeration otherValue)
            return false;

        var typeMatches = GetType() == obj.GetType();
        var valueMatches = Id.Equals(otherValue.Id);

        return typeMatches && valueMatches;
    }

    /// <summary>
    /// Returns the hash code for this enumeration
    /// </summary>
    /// <returns>A hash code for this enumeration</returns>
    public override int GetHashCode() => Id.GetHashCode();

    /// <summary>
    /// Compares this enumeration to another object
    /// </summary>
    /// <param name="other">The other object</param>
    /// <returns>A value indicating the relative order</returns>
    public int CompareTo(object? other)
    {
        if (other is not Enumeration otherEnumeration)
            throw new ArgumentException("Object is not an Enumeration");

        return Id.CompareTo(otherEnumeration.Id);
    }

    /// <summary>
    /// Determines whether two enumerations are equal
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the enumerations are equal; otherwise, false</returns>
    public static bool operator ==(Enumeration? left, Enumeration? right)
    {
        if (left is null && right is null) return true;
        if (left is null || right is null) return false;
        return left.Equals(right);
    }

    /// <summary>
    /// Determines whether two enumerations are not equal
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the enumerations are not equal; otherwise, false</returns>
    public static bool operator !=(Enumeration? left, Enumeration? right)
    {
        return !(left == right);
    }

    /// <summary>
    /// Determines whether one enumeration is less than another
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the first enumeration is less than the second; otherwise, false</returns>
    public static bool operator <(Enumeration left, Enumeration right)
    {
        return left.CompareTo(right) < 0;
    }

    /// <summary>
    /// Determines whether one enumeration is less than or equal to another
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the first enumeration is less than or equal to the second; otherwise, false</returns>
    public static bool operator <=(Enumeration left, Enumeration right)
    {
        return left.CompareTo(right) <= 0;
    }

    /// <summary>
    /// Determines whether one enumeration is greater than another
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the first enumeration is greater than the second; otherwise, false</returns>
    public static bool operator >(Enumeration left, Enumeration right)
    {
        return left.CompareTo(right) > 0;
    }

    /// <summary>
    /// Determines whether one enumeration is greater than or equal to another
    /// </summary>
    /// <param name="left">The first enumeration</param>
    /// <param name="right">The second enumeration</param>
    /// <returns>True if the first enumeration is greater than or equal to the second; otherwise, false</returns>
    public static bool operator >=(Enumeration left, Enumeration right)
    {
        return left.CompareTo(right) >= 0;
    }

    /// <summary>
    /// Parses an enumeration value using the specified predicate
    /// </summary>
    /// <typeparam name="T">The enumeration type</typeparam>
    /// <typeparam name="K">The key type</typeparam>
    /// <param name="value">The value to parse</param>
    /// <param name="description">The description of the value being parsed</param>
    /// <param name="predicate">The predicate to use for matching</param>
    /// <returns>The matching enumeration value</returns>
    /// <exception cref="InvalidOperationException">Thrown when no matching enumeration is found</exception>
    private static T Parse<T, K>(K value, string description, Func<T, bool> predicate) where T : Enumeration
    {
        var matchingItem = GetAll<T>().FirstOrDefault(predicate);

        if (matchingItem == null)
            throw new InvalidOperationException($"'{value}' is not a valid {description} in {typeof(T)}");

        return matchingItem;
    }
}

/// <summary>
/// Generic base class for creating type-safe enumerations with custom value types.
/// This provides more flexibility than the standard Enumeration class by allowing
/// different value types (not just int).
/// </summary>
/// <typeparam name="TEnum">The enumeration type</typeparam>
/// <typeparam name="TValue">The value type</typeparam>
public abstract class Enumeration<TEnum, TValue> : IEquatable<Enumeration<TEnum, TValue>>
    where TEnum : Enumeration<TEnum, TValue>
    where TValue : notnull
{
    private static readonly Lazy<Dictionary<TValue, TEnum>> LazyEnumerations = new(Init);
    private static Dictionary<TValue, TEnum> Enumerations => LazyEnumerations.Value;

    /// <summary>
    /// Gets the value of this enumeration
    /// </summary>
    public TValue Value { get; protected init; }

    /// <summary>
    /// Gets the name of this enumeration value
    /// </summary>
    public string Name { get; protected init; } = string.Empty;

    /// <summary>
    /// Initializes a new instance of the Enumeration class
    /// </summary>
    /// <param name="value"></param>
    /// <param name="name"></param>
    /// <exception cref="ArgumentNullException"></exception>
    protected Enumeration(TValue value, string name)
    {
        if (value is null)
        {
            throw new ArgumentNullException(nameof(value));
        }
        Value = value;
        Name = name;
    }

    /// <summary>
    /// Gets all enumeration values of the specified type by value
    /// </summary>
    /// <param name="value"></param>
    /// <returns></returns>
    public static TEnum? FromValue(TValue value)
    {
        if (value is null)
        {
            return default;
        }
        return Enumerations
            .TryGetValue(value, out var enumeration)
            ? enumeration
            : default;
    }

    /// <summary>
    /// Gets all enumeration values of the specified type by name
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public static TEnum? FromName(string name)
    {
        return Enumerations.Values.SingleOrDefault(x => x.Name == name);
    }

    private static Dictionary<TValue, TEnum> Init()
    {
        var enumType = typeof(TEnum);
        var fields = enumType.GetFields(
                BindingFlags.Public |
                BindingFlags.Static |
                BindingFlags.DeclaredOnly)
            .Where(f => enumType.IsAssignableFrom(f.FieldType))
            .Select(f => (TEnum)f.GetValue(null)!)
            .ToDictionary(x => x.Value, x => x);

        return fields;
    }

    /// <summary>
    /// Determines whether this enumeration is equal to another object
    /// </summary>
    /// <param name="obj"></param>
    /// <returns> a boolean value indicating whether the two objects are equal </returns>
    public override bool Equals(object? obj)
    {
        if (obj is not Enumeration<TEnum, TValue> otherValue)
        {
            return false;
        }

        return Equals(otherValue);
    }

    /// <summary>
    /// Determines whether this enumeration is equal to another enumeration
    /// </summary>
    /// <param name="other"></param>
    /// <returns> a boolean value indicating whether the two enumerations are equal </returns>
    public bool Equals(Enumeration<TEnum, TValue>? other)
    {
        if (other is null)
        {
            return false;
        }

        // Type check is important for correctness
        if (GetType() != other.GetType())
        {
            return false;
        }

        // Use EqualityComparer<TValue> for robust equality, especially for generics
        return EqualityComparer<TValue>.Default.Equals(Value, other.Value);
    }

    /// <summary>
    /// Returns the hash code for this enumeration  
    /// </summary>
    /// <returns> the hash code for this enumeration </returns>
    public override int GetHashCode()
    {
        // Use EqualityComparer<TValue> for robust hash code generation
        return EqualityComparer<TValue>.Default.GetHashCode(Value);
    }
}
