using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.NotificationManagement.ValueObjects;

/// <summary>
/// Value object representing rich notification content
/// </summary>
public class NotificationContent : ValueObject
{
    public string? HtmlContent { get; private set; }
    public string? MarkdownContent { get; private set; }
    public IReadOnlyList<NotificationAttachment> Attachments { get; private set; } = new List<NotificationAttachment>();
    public IReadOnlyDictionary<string, string> Variables { get; private set; } = new Dictionary<string, string>();
    public string? TemplateId { get; private set; }
    public IReadOnlyDictionary<string, object> TemplateData { get; private set; } = new Dictionary<string, object>();

    private NotificationContent() { } // For EF Core

    public NotificationContent(
        string? htmlContent = null,
        string? markdownContent = null,
        IEnumerable<NotificationAttachment>? attachments = null,
        IEnumerable<KeyValuePair<string, string>>? variables = null,
        string? templateId = null,
        IEnumerable<KeyValuePair<string, object>>? templateData = null)
    {
        HtmlContent = htmlContent?.Trim();
        MarkdownContent = markdownContent?.Trim();

        var attachmentList = attachments?.ToList() ?? new List<NotificationAttachment>();
        Attachments = attachmentList.AsReadOnly();

        var variableDict = variables?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, string>();
        Variables = variableDict.AsReadOnly();

        TemplateId = templateId?.Trim();

        var templateDataDict = templateData?.ToDictionary(kvp => kvp.Key, kvp => kvp.Value) ?? new Dictionary<string, object>();
        TemplateData = templateDataDict.AsReadOnly();

        Validate();
    }

    /// <summary>
    /// Creates content with HTML
    /// </summary>
    public static NotificationContent CreateHtml(string htmlContent, IEnumerable<NotificationAttachment>? attachments = null)
    {
        return new NotificationContent(htmlContent: htmlContent, attachments: attachments);
    }

    /// <summary>
    /// Creates content with Markdown
    /// </summary>
    public static NotificationContent CreateMarkdown(string markdownContent, IEnumerable<NotificationAttachment>? attachments = null)
    {
        return new NotificationContent(markdownContent: markdownContent, attachments: attachments);
    }

    /// <summary>
    /// Creates content from template
    /// </summary>
    public static NotificationContent CreateFromTemplate(
        string templateId,
        IEnumerable<KeyValuePair<string, object>> templateData,
        IEnumerable<NotificationAttachment>? attachments = null)
    {
        return new NotificationContent(templateId: templateId, templateData: templateData, attachments: attachments);
    }

    /// <summary>
    /// Adds an attachment
    /// </summary>
    public NotificationContent AddAttachment(NotificationAttachment attachment)
    {
        var newAttachments = new List<NotificationAttachment>(Attachments) { attachment };
        return new NotificationContent(HtmlContent, MarkdownContent, newAttachments, Variables, TemplateId, TemplateData);
    }

    /// <summary>
    /// Removes an attachment
    /// </summary>
    public NotificationContent RemoveAttachment(string fileName)
    {
        var newAttachments = Attachments.Where(a => a.FileName != fileName).ToList();
        return new NotificationContent(HtmlContent, MarkdownContent, newAttachments, Variables, TemplateId, TemplateData);
    }

    /// <summary>
    /// Adds or updates a variable
    /// </summary>
    public NotificationContent SetVariable(string key, string value)
    {
        var newVariables = new Dictionary<string, string>(Variables) { [key] = value };
        return new NotificationContent(HtmlContent, MarkdownContent, Attachments, newVariables, TemplateId, TemplateData);
    }

    /// <summary>
    /// Removes a variable
    /// </summary>
    public NotificationContent RemoveVariable(string key)
    {
        var newVariables = new Dictionary<string, string>(Variables);
        newVariables.Remove(key);
        return new NotificationContent(HtmlContent, MarkdownContent, Attachments, newVariables, TemplateId, TemplateData);
    }

    /// <summary>
    /// Checks if content uses a template
    /// </summary>
    public bool UsesTemplate => !string.IsNullOrWhiteSpace(TemplateId);

    /// <summary>
    /// Checks if content has attachments
    /// </summary>
    public bool HasAttachments => Attachments.Any();

    /// <summary>
    /// Gets the total size of all attachments in bytes
    /// </summary>
    public long TotalAttachmentSize => Attachments.Sum(a => a.SizeInBytes);

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return HtmlContent;
        yield return MarkdownContent;
        foreach (var attachment in Attachments)
            yield return attachment;
        foreach (var variable in Variables.OrderBy(x => x.Key))
        {
            yield return variable.Key;
            yield return variable.Value;
        }
        yield return TemplateId;
        foreach (var data in TemplateData.OrderBy(x => x.Key))
        {
            yield return data.Key;
            yield return data.Value;
        }
    }

    private void Validate()
    {
        if (string.IsNullOrWhiteSpace(HtmlContent) &&
            string.IsNullOrWhiteSpace(MarkdownContent) &&
            string.IsNullOrWhiteSpace(TemplateId))
        {
            throw new ArgumentException("At least one content type (HTML, Markdown, or Template) must be provided");
        }

        if (TotalAttachmentSize > 25 * 1024 * 1024) // 25MB limit
        {
            throw new ArgumentException("Total attachment size cannot exceed 25MB");
        }
    }
}

/// <summary>
/// Value object representing a notification attachment
/// </summary>
public class NotificationAttachment : ValueObject
{
    public string FileName { get; private set; } = null!;
    public string ContentType { get; private set; } = null!;
    public long SizeInBytes { get; private set; }
    public string? Url { get; private set; }
    public byte[]? Data { get; private set; }
    public bool IsInline { get; private set; }
    public string? ContentId { get; private set; }

    private NotificationAttachment() { } // For EF Core

    public NotificationAttachment(
        string fileName,
        string contentType,
        long sizeInBytes,
        string? url = null,
        byte[]? data = null,
        bool isInline = false,
        string? contentId = null)
    {
        SetFileName(fileName);
        SetContentType(contentType);
        SetSizeInBytes(sizeInBytes);
        Url = url?.Trim();
        Data = data;
        IsInline = isInline;
        ContentId = contentId?.Trim();

        Validate();
    }

    /// <summary>
    /// Creates an attachment from URL
    /// </summary>
    public static NotificationAttachment FromUrl(string fileName, string contentType, string url, long sizeInBytes)
    {
        return new NotificationAttachment(fileName, contentType, sizeInBytes, url: url);
    }

    /// <summary>
    /// Creates an attachment from data
    /// </summary>
    public static NotificationAttachment FromData(string fileName, string contentType, byte[] data)
    {
        return new NotificationAttachment(fileName, contentType, data.Length, data: data);
    }

    /// <summary>
    /// Creates an inline attachment
    /// </summary>
    public static NotificationAttachment CreateInline(string fileName, string contentType, byte[] data, string contentId)
    {
        return new NotificationAttachment(fileName, contentType, data.Length, data: data, isInline: true, contentId: contentId);
    }

    /// <summary>
    /// Checks if the attachment is an image
    /// </summary>
    public bool IsImage => ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// Checks if the attachment is a document
    /// </summary>
    public bool IsDocument => ContentType.StartsWith("application/", StringComparison.OrdinalIgnoreCase) ||
                             ContentType.StartsWith("text/", StringComparison.OrdinalIgnoreCase);

    protected override IEnumerable<object?> GetEqualityComponents()
    {
        yield return FileName;
        yield return ContentType;
        yield return SizeInBytes;
        yield return Url;
        yield return Data;
        yield return IsInline;
        yield return ContentId;
    }

    private void SetFileName(string fileName)
    {
        if (string.IsNullOrWhiteSpace(fileName))
            throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

        if (fileName.Length > 255)
            throw new ArgumentException("File name cannot exceed 255 characters", nameof(fileName));

        FileName = fileName.Trim();
    }

    private void SetContentType(string contentType)
    {
        if (string.IsNullOrWhiteSpace(contentType))
            throw new ArgumentException("Content type cannot be null or empty", nameof(contentType));

        ContentType = contentType.Trim().ToLowerInvariant();
    }

    private void SetSizeInBytes(long sizeInBytes)
    {
        if (sizeInBytes < 0)
            throw new ArgumentException("Size cannot be negative", nameof(sizeInBytes));

        if (sizeInBytes > 10 * 1024 * 1024) // 10MB per file
            throw new ArgumentException("File size cannot exceed 10MB", nameof(sizeInBytes));

        SizeInBytes = sizeInBytes;
    }

    private void Validate()
    {
        if (string.IsNullOrWhiteSpace(Url) && (Data == null || Data.Length == 0))
            throw new ArgumentException("Either URL or data must be provided");

        if (!string.IsNullOrWhiteSpace(Url) && Data != null && Data.Length > 0)
            throw new ArgumentException("Cannot provide both URL and data");

        if (Data != null && Data.Length != SizeInBytes)
            throw new ArgumentException("Data length must match size in bytes");
    }
}
