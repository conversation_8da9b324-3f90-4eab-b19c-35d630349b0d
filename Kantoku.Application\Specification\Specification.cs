using System.Linq.Expressions;
using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Application.Specification;


public abstract class Specification<TEntity> : ISpecification<TEntity> where TEntity : Entity
{
    public Expression<Func<TEntity, bool>>? Criteria { get; private set; }
    public Expression<Func<TEntity, object>>? OrderByExpression { get; private set; }
    public Expression<Func<TEntity, object>>? OrderByDescendingExpression { get; private set; }
    public Expression<Func<TEntity, object>>? GroupByExpression { get; private set; }
    public List<Expression<Func<TEntity, object>>> IncludesExpressions { get; } = [];

    public bool IsPagingEnabled { get; private set; }
    public int Take { get; private set; }
    public int Skip { get; private set; }

    protected Specification(Expression<Func<TEntity, bool>> criteria)
     => Criteria = criteria;

    protected Specification()
        => Criteria = _ => true;

    protected virtual void ApplyPaging(int skip, int take)
    {
        Skip = skip;
        Take = take;
        IsPagingEnabled = true;
    }

    protected virtual void ApplyOrderBy(Expression<Func<TEntity, object>> orderByExpression)
        => OrderByExpression = orderByExpression;

    protected virtual void ApplyOrderByDescending(Expression<Func<TEntity, object>> orderByDescendingExpression)
        => OrderByDescendingExpression = orderByDescendingExpression;

    protected virtual void ApplyGroupBy(Expression<Func<TEntity, object>> groupByExpression)
        => GroupByExpression = groupByExpression;

    protected virtual void AddInclude(Expression<Func<TEntity, object>> includeExpression)
        => IncludesExpressions.Add(includeExpression);
}