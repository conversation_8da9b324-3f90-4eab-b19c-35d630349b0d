using Kantoku.SharedKernel.BuildingBlocks;

namespace Kantoku.Domain.AccountManagement;

/// <summary>
/// Entity representing an active session for an account
/// </summary>
public class AccountSession : Entity<Guid>
{
    public Guid AccountId { get; private set; }
    public string SessionToken { get; private set; } = null!;
    public string? RefreshToken { get; private set; }
    public DateTime CreatedDate { get; private set; }
    public DateTime ExpiryDate { get; private set; }
    public DateTime? LastAccessDate { get; private set; }
    public string? IpAddress { get; private set; }
    public string? UserAgent { get; private set; }
    public string? DeviceInfo { get; private set; }
    public bool IsActive { get; private set; } = true;
    public DateTime? RevokedDate { get; private set; }
    public string? RevokedReason { get; private set; }

    // Private constructor for EF Core
    private AccountSession() : base() { }

    /// <summary>
    /// Creates a new account session
    /// </summary>
    public AccountSession(
        Guid id,
        Guid accountId,
        string sessionToken,
        DateTime expiryDate,
        string? refreshToken = null,
        string? ipAddress = null,
        string? userAgent = null,
        string? deviceInfo = null) : base(id)
    {
        AccountId = accountId;
        SetSessionToken(sessionToken);
        RefreshToken = refreshToken;
        CreatedDate = DateTime.UtcNow;
        ExpiryDate = expiryDate;
        LastAccessDate = DateTime.UtcNow;
        IpAddress = ipAddress?.Trim();
        UserAgent = userAgent?.Trim();
        DeviceInfo = deviceInfo?.Trim();
    }

    /// <summary>
    /// Updates the last access time
    /// </summary>
    public void UpdateLastAccess()
    {
        if (!IsValid)
            throw new InvalidOperationException("Cannot update access time for invalid session");

        LastAccessDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Extends the session expiry time
    /// </summary>
    public void ExtendSession(DateTime newExpiryDate)
    {
        if (!IsValid)
            throw new InvalidOperationException("Cannot extend invalid session");

        if (newExpiryDate <= DateTime.UtcNow)
            throw new ArgumentException("New expiry date must be in the future", nameof(newExpiryDate));

        ExpiryDate = newExpiryDate;
        LastAccessDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Revokes the session
    /// </summary>
    public void Revoke(string reason = "Manual revocation")
    {
        if (!IsActive)
            return; // Already revoked

        IsActive = false;
        RevokedDate = DateTime.UtcNow;
        RevokedReason = reason;
    }

    /// <summary>
    /// Updates the refresh token
    /// </summary>
    public void UpdateRefreshToken(string refreshToken)
    {
        if (!IsValid)
            throw new InvalidOperationException("Cannot update refresh token for invalid session");

        RefreshToken = refreshToken;
        LastAccessDate = DateTime.UtcNow;
    }

    /// <summary>
    /// Checks if the session is valid (active and not expired)
    /// </summary>
    public bool IsValid => IsActive && DateTime.UtcNow <= ExpiryDate;

    /// <summary>
    /// Checks if the session is expired
    /// </summary>
    public bool IsExpired => DateTime.UtcNow > ExpiryDate;

    /// <summary>
    /// Gets the remaining session time
    /// </summary>
    public TimeSpan? RemainingTime
    {
        get
        {
            if (!IsValid)
                return null;

            var remaining = ExpiryDate - DateTime.UtcNow;
            return remaining > TimeSpan.Zero ? remaining : TimeSpan.Zero;
        }
    }

    /// <summary>
    /// Gets the session duration
    /// </summary>
    public TimeSpan SessionDuration
    {
        get
        {
            var endTime = RevokedDate ?? (IsExpired ? ExpiryDate : DateTime.UtcNow);
            return endTime - CreatedDate;
        }
    }

    // Private helper methods
    private void SetSessionToken(string sessionToken)
    {
        if (string.IsNullOrWhiteSpace(sessionToken))
            throw new ArgumentException("Session token cannot be null or empty", nameof(sessionToken));

        SessionToken = sessionToken;
    }
}
